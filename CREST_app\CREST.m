function CREST(globalCtlFile,slopeMode,nCore)


    disp('%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%')
    disp('%         CREST v2.1, released Jul. 2015         %')
    disp('% COUPLED ROUTING AND EXCESS STORAGE (OU & NASA) %')
    disp('%      contact: <EMAIL>             %')
    disp('%                 <EMAIL>                %')
    disp('%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%')
    disp('configuring environment...')
    curFile=mfilename('fullpath');
    [curDir,~,~]=fileparts(curFile);
    [progDir,~,~]=fileparts(curDir);
    dllDir=[progDir,'\DLL'];
    setenv('PATH',[getenv('PATH'),';',dllDir]);
    addpath([progDir,'/MEX']);
    addpath([progDir,'/optimization']);
    addpath([progDir,'/optimization/SCE_par']);
    addpath([progDir,'/optimization/SCE_Seq']);
    sysBit=mexext;
    if strcmpi(sysBit,'mexw64')~=1
        warning(sprintf('not a windows x64 system.\n Users need to compile the GDAL library themselves.'))
    end
    disp('loading GDAL v1.11.0...')
    GDALLoad();
    globalPar=GlobalParameters(globalCtlFile);
    if globalPar.numOfLoaded>0
        startDate=globalPar.warmupDate;
    else
        startDate=globalPar.startDate;
    end


    basinVar=BasinVariables(globalPar.basicPath,globalPar.basicFormat,globalPar.timeMark);

    hydroSites=HydroSites(globalPar.out_shp,basinVar.geoTrans,basinVar.spatialRef,...
    globalPar.obsNoData,globalPar.nTimeSteps,startDate,globalPar.endDate,max(1,globalPar.numOfLoaded),...
    globalPar.timeStep,globalPar.warmupDate);
    hydroSites.ImportObservation(globalPar.obsPath,globalPar.obsFormat,globalPar.out_STCD);
    basinVar.CalSlope(slopeMode,hydroSites.row(hydroSites.indexOutlets),hydroSites.col(hydroSites.indexOutlets));
    basinVar.GetSubMasks(hydroSites.row,hydroSites.col,hydroSites.STCD,globalPar.resPath);

    modelPar=ModelParameters(globalPar.paramPath,basinVar.basinMask,globalPar.useLAI,basinVar.geoTrans,basinVar.spatialRef);

    stateVar=StateVariables(hydroSites,globalPar.resPath,...
    globalPar.timeMark,globalPar.timeStepInM,...
    basinVar.basinMask,basinVar.stream,...
    globalPar.ICSPath,basinVar.geoTrans,basinVar.spatialRef);


    forcingVar=ForcingVariables(globalPar.useLAI,...
    basinVar.basinMask,...
    startDate,globalPar.endDate,globalPar.timeStep,globalPar.timeFormat,...
    globalPar.timeMark,basinVar.geoTrans,basinVar.spatialRef,...
    globalPar.PETPathExt,globalPar.rainPathExt,globalPar.LAIPathExt,...
    globalPar.PETDateFormat,globalPar.rainDateFormat,globalPar.LAIDateFormat,...
    globalPar.PETDateConvention,globalPar.rainDateConvention,...
    globalPar.PETFormat,globalPar.rainFormat,globalPar.LAIFormat,...
    globalPar.PETTsScaling,globalPar.RainTsScaling,globalPar.LAITsScaling,...
    globalPar.PETDateStart,globalPar.rainDateStart,globalPar.LAIDateStart,...
    globalPar.PETDateInterval,globalPar.rainDateInterval,globalPar.LAIDateInterval,...
    globalPar.PETPathInt,globalPar.rainPathInt,globalPar.LAIPathInt,...
    globalPar.decompBeforeSrc,globalPar.decompBeforeDst,globalPar.OS);

    simulator=CREST_Simulator(modelPar,basinVar,stateVar,forcingVar,globalPar);

tic
    switch globalPar.runStyle
    case 'simu'
        [NSCE,~]=simulator.Simulate();
        stateVar.SaveStates();
    case 'cali_SCEUA'
        warning('off','all');

        funcHandle=@Simulate;
        optimizer=SCEUA_Optimizer(globalPar.calibPath,globalPar.resPath,...
        funcHandle,simulator,nCore);
        if strcmpi(globalPar.calibMode,'parallel')
            optimizer.optimize('par');
        else
            optimizer.optimize('seq');
        end
        optimizer.exportRes(globalPar.resPath);
    end
toc
    disp('done!');
end
