classdef ModelParameters<RasterVariables

    properties
        rainFact0;rainFact;
        Ksat0;Ksat;
        WM0;WM;
        WMM;
        B0;B;
        IM0;IM;
        KE0;KE;
        coeM0;coeM;
        expM0;expM;
        coeR0;coeR;
        coeS0;coeS;
        KS0;KS;
        KI0;KI;
        LCC;
        keywordsType={'RainFactType','KsatType','WMType',...
        'BType','IMType','KEType',...
        'coeMType','expMType','coeRType',...
        'coeSType','KSType','KIType'};
        keywordsVar={'RainFact','Ksat','WM',...
        'B','IM','KE',...
        'coeM','expM','coeR',...
        'coeS','KS','KI'};

        parType={'bDistRainFact','bDistKsat','bDistWM',...
        'bDistB','bDistIM','bDistKE',...
        'bDistcoeM','bDistexpM','bDistcoeR',...
        'bDistcoeS','bDistKS','bDistKI'};
        parVar={'rainFact','Ksat','WM',...
        'B','IM','KE',...
        'coeM','expM','coeR',...
        'coeS','KS','KI'};
        nModelPar;
        calibIndices;
        scale;
    end
    properties(Access=private)

        rows,columns;
        bDistRainFact;bDistKsat;bDistWM;bDistB;
        bDistIM;bDistKE;bDistcoeM;bDistexpM;bDistcoeR;
        bDistcoeS;bDistKS;bDistKI;
        useLAI;

        keywordsLAI={'LAI1','LAI2','LAI3','LAI4','LAI5','LAI6','LAI7','LAI8','LAI9','LAI10','LAI11','LAI12','LAI13'};
    end
    methods
        function obj=ModelParameters(pDir,basinMask,useLAI,geoTransBasic,spatialRefBasic)
            obj.basinMask=basinMask;
            obj.useLAI=useLAI;
            obj.geoTrans=geoTransBasic;
            obj.spatialRef=spatialRefBasic;
            pFile=ModelParameters.GenerateFileNames(pDir);
            pDir=[fileparts(pFile),'\'];
            pFileID=fopen(pFile);
            commentSymbol='#';
            obj.nModelPar=length(obj.keywordsVar);
            for i=1:obj.nModelPar

                cmdInitialize=strcat('obj.',obj.parVar{i},'=obj.Initialize();');
                cmdInitialize2=strcat('obj.',obj.parVar{i},'0=obj.Initialize();');
                cmdFrmGlobal=strcat('[value,bDistr]=ModelParameters.readVarInfo(pFileID,''',...
                obj.keywordsType{i},''',''',obj.keywordsVar{i},''',''',commentSymbol,''');');

                cmdToVar2=strcat('if ~bDistr',' obj.',obj.parVar{i},...
                '0(obj.basinMask)= value; else parFile=[pDir,value];',...
                ' obj.',obj.parVar{i},'0= ReadRaster(parFile); end');
                eval(cmdInitialize);
                eval(cmdInitialize2);
                eval(cmdFrmGlobal);

                eval(cmdToVar2);
            end
            if obj.useLAI
                [strLCC,~]=ModelParameters.readVarInfo(pFileID,'',commentSymbol);

            end
            fclose(pFileID);
            obj.WMM=obj.Initialize();
            obj.scale=ones(obj.nModelPar,1);
            obj.ModelParReInitialize();
        end
        function ModelParReInitialize(obj,x0,calibKeywords)
            if nargin==3
                obj.calibIndices=ones(length(calibKeywords),1);
                for i=1:length(calibKeywords)
                    keyword=calibKeywords{i};
                    obj.calibIndices(i)=find(strcmp(keyword,obj.keywordsVar));
                end
                obj.scale(obj.calibIndices)=x0;
            end
            for i=1:obj.nModelPar
                cmdAmp=strcat('obj.',obj.parVar{i},...
                '(obj.basinMask)=obj.',obj.parVar{i},...
                '0(obj.basinMask)*obj.scale(i);');
                eval(cmdAmp);
            end
        end
        function AfterModelParUpdate(obj)



            obj.WMM(obj.basinMask)=obj.WM(obj.basinMask).*(1.0+obj.B(obj.basinMask));
        end
    end
    methods(Static)
        function fileModelPar=GenerateFileNames(parDir)
            fileModelPar=parDir;
        end
    end
end