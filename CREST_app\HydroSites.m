classdef HydroSites<handle
    properties
        STCD;
        indexOutlets;
        nSites;
        row;col;
        runoff;
        noObserv;
        startDate;
        warmupDate;
        endDate;
        nTimeSteps;
        timeStep;
        nPeriods;
    end
    methods
        function obj=HydroSites(shapefile,geoTransTar,spatialRefTar,...
            noObserv,nTimeSteps,startDate,endDate,numOfLoaded,...
            timeStep,warmupDate)
            [xLoc,yLoc,spatialRef,obj.STCD]=readShapeLoc(shapefile,0);
            [XTar,YTar]=ProjTransform(spatialRef,spatialRefTar,xLoc,yLoc);
            [obj.row,obj.col]=Proj2RowCol(geoTransTar,YTar,XTar);
            obj.nSites=length(obj.STCD);
            obj.noObserv=noObserv;
            obj.nTimeSteps=nTimeSteps;
            obj.startDate=startDate;
            obj.nPeriods=numOfLoaded;
            obj.endDate=endDate;
            obj.timeStep=timeStep;
            obj.warmupDate=warmupDate;
        end
        function ImportObservation(obj,dirObs,obsFormat,STCDOutlets)
            disp('reading observation data');
            fileObs=obj.GenerateFileNames(dirObs);
            if~isempty(STCDOutlets)
                [~,obj.indexOutlets]=ismember(STCDOutlets,obj.STCD);
            else
                obj.indexOutlets=[];
            end
            for ip=1:obj.nPeriods
                iRunoff=obj.noObserv*ones(round((obj.endDate(ip)-obj.warmupDate(ip))/obj.timeStep),obj.nSites);
                nValidate=round((obj.endDate(ip)-obj.warmupDate(ip))/obj.timeStep);
                nWarmup=round((obj.warmupDate(ip)-obj.startDate(ip))/obj.timeStep)+1;
                if any(obj.indexOutlets==0)
                    error('one or more information of outlets are not provided');
                end
                for i=1:obj.nSites
                    fileName=fileObs{i};
                    formatSpec='%f,%f';
                    if isscalar(obj.indexOutlets)
                        if i==obj.indexOutlets&&exist(fileName,'file')~=2
                            error('observation of outlets must be provided');
                        end
                    end
                    if exist(fileName,'file')==2

                        fid=fopen(fileName);
                        head=textscan(fid,'%s',1);
                        raw=textscan(fid,formatSpec);
                        fclose(fid);
                        if isnumeric(raw{1}(1))

                            obsDateTimeStr=num2str(raw{1});
                        elseif ischar(raw{1}{1})
                            obsDateTimeStr=char(raw{1});
                        end
                        if isempty(obsFormat)
                            obsDateTime=datenum(obsDateTimeStr);
                        else
                            obsDateTime=datenum(obsDateTimeStr,obsFormat);
                        end
                        data=raw{2};
                        clear raw
                        indexObsInModel=(obsDateTime-obj.warmupDate(ip))/obj.timeStep;
                        rIndexObsInModel=round(indexObsInModel);
                        indexObsInModel=rIndexObsInModel.*...
                        (abs(rIndexObsInModel-indexObsInModel)<1e-4)+...
                        indexObsInModel.*(abs(rIndexObsInModel-indexObsInModel)>=1e-4);
                        indexObs=(1:size(data,1))';
                        indexInRange=logical((indexObsInModel>=1).*(indexObsInModel<=nValidate));
                        iRunoff(indexObsInModel(indexInRange),i)=data(indexObs(indexInRange));
                    end
                end
                if obj.nPeriods==1&&obj.warmupDate(ip)>obj.startDate(ip)
                    iRunoff=[obj.noObserv*ones(nWarmup,obj.nSites);iRunoff];
                end
                obj.runoff=[obj.runoff;iRunoff];
            end
        end
    end
    methods(Access=private)
        function fileObs=GenerateFileNames(obj,dirObs)
            fileObs=cell(obj.nSites,1);
            for i=1:obj.nSites;
                if ischar(obj.STCD{i})
                    fileObs{i}=strcat(dirObs,obj.STCD{i},'_obs.csv');
                else
                    fileObs{i}=strcat(dirObs,num2str(obj.STCD{i}),'_obs.csv');
                end
            end
        end
    end
end