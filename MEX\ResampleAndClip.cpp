/* GDAL MEX Utility (v1.0) by <PERSON>,Xinyi
   contact: <PERSON><PERSON><PERSON>.<PERSON>@uconn.edu,Feb, 2015
   reproject, resample and clip */
/************ calling convention *****************/
/* ResampleAndClip(geoTransTar,wktTar,tarXSize,tarYSize,strSrcFile,strDstFile,fmt)
/****************************************************/
#include "mexOperation.h"
#include "gdal.h"
#include "ogr_core.h"
#include "gdalwarper.h"
#include "cpl_conv.h"
#include "cpl_string.h"
#include "mex.h"
#ifdef __linux__
    #pragma comment (lib,"libgdal.so")
    #define strEqual 1
    //linux code goes here
#elif _WIN64
    // windows code goes here
    #pragma comment (lib,"gdal_i.lib")
    #define strEqual 0
#else
    #error Platform not supported
#endif
void mexFunction( int nlhs, mxArray *plhs[], int nrhs, const mxArray *prhs[] )
{
    /*************definition of input arguments****************/
    double* geoTransTar;
    const char *wktTar, *strSrcFile, *strDstFile, *fmt;
    int tarXSize,tarYSize;
    /***********************************************************/
    char *ext; 
    GDALDatasetH dsSrcH, dsDstH;
    GDALDriverH dstDriverH;
    GDALRasterBandH bandSrcH,bandDstH;
    OGRSpatialReferenceH srSrcH;
    const char *wktSrc;
    char *projWG84;
    GDALDataType dataType;
    double geoTransSrc[6],resScalingRatioX,resScalingRatioY;
    GDALResampleAlg resampleAlg;
    GDALWarpOptions *wos;
    int bSuccess;
    double NoData;
    /*************Converting Input Arguments*****************/
    if(nlhs>0)
        mexErrMsgTxt("no output");
    geoTransTar=mxGetPr(prhs[0]);
    wktTar=ImportString(prhs[1]);
    tarXSize=(int)(*mxGetPr(prhs[2]));
    tarYSize=(int)(*mxGetPr(prhs[3]));
    strSrcFile=ImportString(prhs[4]);
    strDstFile=ImportString(prhs[5]);
    fmt=ImportString(prhs[6]);
    /********************************************************/
    ext=fileExtension(strSrcFile);
    if (strcmpi(ext,".HDF")!=strEqual)
    {
        dsSrcH = GDALOpen(strSrcFile, GA_ReadOnly);
    }
    mxFree((void *)strSrcFile);
    bandSrcH=GDALGetRasterBand(dsSrcH,1);
    dataType=GDALGetRasterDataType(bandSrcH);
    wktSrc = GDALGetProjectionRef(dsSrcH);
    srSrcH=OSRNewSpatialReference (wktSrc);
    if(strlen(wktSrc)==0)
    {
        // the forcing data has no projection info
        // we brutally assign a WGS84 Geographic CS for it
        OSRSetWellKnownGeogCS(srSrcH,"WGS84");
        OSRExportToWkt(srSrcH,&projWG84);
        GDALSetProjection(dsSrcH,projWG84);
    }
    GDALGetGeoTransform(dsSrcH,geoTransSrc);
    
    if (IsGeographic(wktTar,geoTransTar))
    {
        if (!IsGeographic(wktSrc,geoTransSrc))
        {
            resScalingRatioX=(geoTransTar[1]+geoTransTar[2])*110574/(geoTransSrc[1]+geoTransSrc[2]);
            resScalingRatioY=fabs((geoTransTar[4]+geoTransTar[5])*110574/(geoTransSrc[4]+geoTransSrc[5]));
        }
        else // if the projection file is missing, CS is considered as GCS srSrc.IsGeographic
        {
            resScalingRatioX=(geoTransTar[1]+geoTransTar[2])/(geoTransSrc[1]+geoTransSrc[2]);
            resScalingRatioY=fabs((geoTransTar[4]+geoTransTar[5])/(geoTransSrc[4]+geoTransSrc[5]));
        }
    }
    else
    {
        if (!IsGeographic(wktSrc,geoTransSrc))
        {
            resScalingRatioX=(geoTransTar[1]+geoTransTar[2])/(geoTransSrc[1]+geoTransSrc[2]);
            resScalingRatioY=fabs((geoTransTar[4]+geoTransTar[5])/(geoTransSrc[4]+geoTransSrc[5]));
        }
        else// srSrc.IsGeographic
        {
            resScalingRatioX=(geoTransTar[1]+geoTransTar[2])/((geoTransSrc[1]+geoTransSrc[2])*110574);
            resScalingRatioY=fabs((geoTransTar[4]+geoTransTar[5])/((geoTransSrc[4]+geoTransSrc[5])*110574));
        }
    }
    //the scaling ratio is set 1 if not far from 1
    if  (resScalingRatioX<=1.2) // no average
        resampleAlg=GRA_NearestNeighbour;
    else if (resScalingRatioX>1.2 && resScalingRatioX<2)//bilinear
        resampleAlg=GRA_Bilinear;
    else if (resScalingRatioX>=2)//average
        resampleAlg=GRA_Average;
    // create the output file and dataset
    dstDriverH = GDALGetDriverByName(fmt);
    mxFree((void *)fmt);
    dsDstH = GDALCreate(dstDriverH,strDstFile,tarXSize,tarYSize, 1, dataType, NULL);
    // set the geotransformation coefficients
    GDALSetGeoTransform (dsDstH, geoTransTar);
    // set the projection of the output file
    GDALSetProjection (dsDstH, wktTar);
    //GDALSetRasterNoDataValue(GDALGetRasterBand(dsDstH,1),noDataVal);
    mxFree((void *)wktTar);
    /************* set the warp options ********************/
    wos = GDALCreateWarpOptions();
    wos->papszWarpOptions = CSLDuplicate(NULL);  
    wos->hSrcDS = dsSrcH;
    wos->hDstDS = dsDstH;
    wos->nBandCount = 1;
    wos->panSrcBands = (int *) CPLMalloc(1*sizeof(int));  
    wos->panDstBands = (int *) CPLMalloc(1*sizeof(int));  
    wos->panSrcBands[0] = 1;
    wos->panDstBands[0] = 1; 
    wos->eWorkingDataType = dataType;  
    wos->eResampleAlg = resampleAlg;
    wos->pfnTransformer = GDALGenImgProjTransform;  
    wos->pTransformerArg = GDALCreateGenImgProjTransformer2(dsSrcH, dsDstH, NULL);
    /***************************************************************************/
    /************* set the warp operation and execute********************/
    GDALWarpOperation oOperation;
    oOperation.Initialize(wos);
    oOperation.ChunkAndWarpImage( 0, 0,tarXSize, tarYSize );
    GDALDestroyGenImgProjTransformer(wos->pTransformerArg);  
    
    /********************* set the noData ********************************/
    NoData=GDALGetRasterNoDataValue(bandSrcH,&bSuccess);
    if (bSuccess)
    {
        bandDstH=GDALGetRasterBand(dsDstH,1);
        GDALSetRasterNoDataValue(bandDstH,NoData);
    }
    /*************release resources*****************************************/
    GDALDestroyWarpOptions(wos); 
    GDALClose((GDALDatasetH) dsSrcH);  
    GDALClose((GDALDatasetH) dsDstH);
    /***************************************************************************/
//     delete wktTar;
//     delete strSrcFile;
//     delete strDstFile;
//     delete fmt;
}
