function bGCS = IsGeographic(spatialRef, geoTrans)
% IsGeographic - Replacement for MEX function to determine if coordinate system is geographic
% 
% Input:
%   spatialRef - Spatial reference string
%   geoTrans - Geotransform array [6x1]
%
% Output:
%   bGCS - Boolean indicating if coordinate system is geographic (lat/lon)

% Default to false
bGCS = false;

try
    % Check if spatialRef contains geographic coordinate system indicators
    if ischar(spatialRef) && ~isempty(spatialRef)
        spatialRefUpper = upper(spatialRef);
        
        % Look for common geographic coordinate system indicators
        geoIndicators = {'GEOGCS', 'GEOGRAPHIC', 'LATITUDE', 'LONGITUDE', ...
                        'WGS84', 'WGS_1984', 'GCS_', 'DEGREE', 'DECIMAL_DEGREE'};
        
        for i = 1:length(geoIndicators)
            if contains(spatialRefUpper, geoIndicators{i})
                bGCS = true;
                return;
            end
        end
        
        % Check for projected coordinate system indicators (these would make it NOT geographic)
        projIndicators = {'PROJCS', 'PROJECTED', 'UTM', 'METER', 'METRE', ...
                         'TRANSVERSE_MERCATOR', 'LAMBERT', 'ALBERS'};
        
        for i = 1:length(projIndicators)
            if contains(spatialRefUpper, projIndicators{i})
                bGCS = false;
                return;
            end
        end
    end
    
    % If spatial reference is not conclusive, check geotransform
    if nargin >= 2 && ~isempty(geoTrans) && length(geoTrans) >= 6
        % For geographic coordinates, pixel sizes are typically small (degrees)
        % For projected coordinates, pixel sizes are typically larger (meters)
        pixelSizeX = abs(geoTrans(2));
        pixelSizeY = abs(geoTrans(6));
        
        % If pixel sizes are very small (< 1), likely geographic coordinates
        if pixelSizeX < 1 && pixelSizeY < 1
            bGCS = true;
        % If pixel sizes are large (> 1), likely projected coordinates  
        elseif pixelSizeX > 1 && pixelSizeY > 1
            bGCS = false;
        end
    end
    
catch ME
    % If any error occurs, default to false and display warning
    warning('IsGeographic: Error determining coordinate system type: %s. Assuming projected coordinates.', ME.message);
    bGCS = false;
end

end
