/* GDAL MEX Utility (v1.0) by <PERSON>,Xinyi
   contact: <PERSON><PERSON><PERSON><PERSON><PERSON>@uconn.edu,Feb, 2015
   read raster from a single-band image file */
//calling convention
//[raster,geoTrans,proj,dataType]=ReadRaster(fileName);
//[raster,geoTrans,proj,dataType]=ReadRaster(fileName,startRow,startCol,rows,cols);
#include "mexOperation.h"
#include "gdal.h"
#include "mex.h"
#ifdef __linux__
    #pragma comment (lib,"libgdal.so")
    //linux code goes here
#elif _WIN64
    // windows code goes here
    #pragma comment (lib,"gdal_i.lib")
#else
    #error Platform not supported
#endif


void mexFunction( int nlhs, mxArray *plhs[], int nrhs, const mxArray *prhs[] )
{
    /* input variables*/
	mxArray* mxSrcFile;
	int strLen,status;
	char* pszSrcFile;
	GDALDatasetH pDSrc;
    GDALRasterBandH pBand;
    
    int x0,y0,rows,cols,xSize,ySize;
    double* raster;
    /*output variables*/
    int CPLErr,bSuccess;
    mxArray *mxRaster;
    //geoTransform
    double geoTrans[6];mxArray *mxGeoTrans;
    
    //spatial Reference
    const char *spatialRef;mxArray *mxProjection;
    // data type
    GDALDataType DataType;mxArray *mxDataType;
    
    double NoData;mxArray* mxNoData;
    int out_type_size;
    void* dptr;
    mxArray* mxInput[2];
	if ( nrhs !=1 && nrhs!=5 )
		mexErrMsgTxt("incorrect input"); 
    pszSrcFile=ImportString((mxArray *)prhs[0]);
    //Start GDAL Operations
	pDSrc = (GDALDatasetH)GDALOpen(pszSrcFile, GA_ReadOnly);
    mxFree(pszSrcFile);
    pBand=GDALGetRasterBand(pDSrc,1);
    DataType=GDALGetRasterDataType(pBand);
    xSize=GDALGetRasterXSize(pDSrc);
    ySize=GDALGetRasterYSize(pDSrc);
    if (nrhs==1)// read the entire image
    {
        x0=0;
        y0=0;
        cols=xSize;
        rows=ySize;
    }
    else // read a specified block
    {
        x0 = *(mxGetPr(prhs[2]))-1;
        y0 = *(mxGetPr(prhs[1]))-1;
        cols=*(mxGetPr(prhs[4]));
        rows=*(mxGetPr(prhs[3]));
        if (x0+cols>xSize-1 || y0+rows>ySize-1)
            mexErrMsgTxt ( "Extent exceeds the image size" );
    }
    raster=new double[rows*cols];
    GDALRasterIO(pBand,GF_Read,x0,y0, cols, rows, raster, 
            cols, rows, GDT_Float64, 0,0);
    out_type_size = GDALGetDataTypeSize ( GDT_Float64 ) / 8;
    mxRaster=ExportRealMatrix((const double*)raster,rows,cols,out_type_size,1);
    delete raster;
    if (nlhs<1)
        mexErrMsgTxt ( "output arguments not assigned" );
    else
    {
        NoData=GDALGetRasterNoDataValue(pBand,&bSuccess);
        mxNoData=mxCreateDoubleScalar(NoData);
        mxInput[0]=mxRaster;
        mxInput[1]=mxNoData;
        mexCallMATLAB(1,&mxRaster,2,mxInput,"SetNull");
        plhs[0] = mxRaster;
        if (nlhs>=2)
        {
            CPLErr=GDALGetGeoTransform(pDSrc,geoTrans);
            if (CPLErr!=CE_None)
                mexErrMsgTxt("error in reading head"); 
            mxGeoTrans=ExportRealMatrix((const double*)geoTrans,1,6,out_type_size,0);
            plhs[1]=mxGeoTrans;
        }
        if (nlhs>=3)
        {
            spatialRef=GDALGetProjectionRef (pDSrc);
            mxProjection=mxCreateString(spatialRef);
            plhs[2]=mxProjection;
        }
        if (nlhs>=4)
        {
            mxDataType=mxCreateDoubleScalar(DataType);
            plhs[3]=mxDataType;
        }
    }
    GDALClose((GDALDatasetH) pDSrc); 
}