classdef CREST_Simulator < matlab.mixin.Copyable

    properties
        basicVar;
        modelPar;
        stateVar;
        forcingVar;
        globalVar;
    end

    properties (Access = private)

        WM; B; IM; Ksat; KS; KI; WMM;

        excS; excI;
        W0;
        PET; rain;
        EAct;
        SS0; SI0; RS; RI;
        runoff;

        NaNIndex;
        SIndexA; SFracA; uSIndexA; SIndexAValid;
        SIndexB; SFracB; uSIndexB; SIndexBValid;
        IIndexA; IFracA; uIIndexA; IIndexAValid;
        IIndexB; IFracB; uIIndexB; IIndexBValid;

        RSPassedIndex; uRSPassedIndex; RSStartedIndex;
        RIPassedIndex; uRIPassedIndex; RIStartedIndex;

        gridArea;
        SgridAreaA; SgridAreaB; IgridAreaA; IgridAreaB;
        countValid;
        rTimeUnit
    end

    methods

        function obj = CREST_Simulator(mPar, bVar, sVar, fVar, gVar)
            obj.modelPar = mPar;
            obj.stateVar = sVar;
            obj.forcingVar = fVar;
            obj.basicVar = bVar;
            obj.globalVar = gVar;

            switch obj.globalVar.timeMark
                case 'd'
                    obj.rTimeUnit = 1/24;
                case 'h'
                    obj.rTimeUnit = 1;
                case 'u'
                    obj.rTimeUnit = 60;
            end

        end

        function [NSCE, tElapse] = Simulate(obj, x0, keywords)

            t1 = cputime;

            if nargin == 3
                mode = 'calib';
            else
                mode = 'simu';
            end

            if strcmpi(mode, 'calib')

                obj.ModelParInit(x0, keywords);
            else
                obj.ModelParInit();
            end

            obj.stateVar.preset();

            if obj.globalVar.numOfLoaded > 0
                obj.forcingVar.reset(mode, true);
                obj.stateVar.LoadModelStates([obj.globalVar.statePath, ...
                                                  datestr(obj.forcingVar.dateCur, obj.globalVar.timeFormat), '.mat']);
                obj.forcingVar.MoveNext(mode);
            else
                obj.forcingVar.reset(mode, false);
            end

            obj.stateVar.reset(obj.modelPar.WM);

            bEnd = false;
            timeOfStep = 1;

            while (~bEnd)

                if ~strcmpi(mode, 'calib')
                    disp(datestr(obj.forcingVar.dateCur, 'yyyy/mm/dd:HH:MM'))
                end

                obj.stateVar.rainAct(obj.forcingVar.basinMask) = obj.forcingVar.rain(obj.forcingVar.basinMask) * ...
                    obj.globalVar.timeStepInM;
                obj.stateVar.rain(obj.forcingVar.basinMask) = obj.stateVar.rainAct(obj.forcingVar.basinMask) .* obj.modelPar.rainFact(obj.forcingVar.basinMask);

                obj.stateVar.PET(obj.forcingVar.basinMask) = obj.forcingVar.PET(obj.forcingVar.basinMask) * ...
                    obj.globalVar.timeStepInM .* obj.modelPar.KE(obj.forcingVar.basinMask);

                obj.DirectRunoffGen();

                obj.WaterBudgetUpdate();

                obj.StateVarUpdate();

                obj.DownstreamRoute();

                obj.stateVar.CalculateOutletData(timeOfStep, obj.modelPar.WM, strcmpi(mode, 'calib'), obj.basicVar.masks);
                obj.stateVar.OutputVar(obj.forcingVar.dateCur, ...
                    obj.globalVar.output_Rain, obj.globalVar.output_EPot, obj.globalVar.output_EAct, ...
                    obj.globalVar.output_SM, obj.globalVar.output_W, ...
                    obj.globalVar.output_runoff, ...
                    obj.globalVar.output_ExcS, obj.globalVar.output_ExcI, ...
                    obj.globalVar.output_RS, obj.globalVar.output_RI);

                if ~strcmpi(mode, 'calib')

                    if ismember(obj.forcingVar.dateCur, obj.globalVar.saveDates)
                        dateToSave = datestr(obj.forcingVar.dateCur + obj.globalVar.saveOffset, obj.globalVar.saveDateFormat);
                        obj.stateVar.SaveModelStates([obj.globalVar.statePath, dateToSave, '.mat'], ...
                            obj.modelPar.WM);
                    end

                end

                timeOfStep = timeOfStep + 1;
                bEnd = ~obj.forcingVar.MoveNext(mode);

                if bEnd && obj.globalVar.numOfLoaded > 0
                    bEnd = obj.forcingVar.reset(mode, true);

                    if bEnd
                        continue;
                    end

                    fileLoad = [obj.globalVar.statePath, ...
                                  datestr(obj.forcingVar.dateCur, obj.globalVar.timeFormat), '.mat'];
                    obj.stateVar.LoadModelStates(fileLoad);
                    obj.stateVar.reset(obj.modelPar.WM);
                    obj.forcingVar.MoveNext(mode);
                end

            end

            if ~strcmpi(mode, 'calib')
                [NSCE, Bias, CC] = obj.stateVar.GetSingleObjNSCE();
                disp(strcat('NSCE=', num2str(NSCE)))
                disp(strcat('Bias=', num2str(Bias)))
                disp(strcat('CC=  ', num2str(CC)))
            else
                NSCE = obj.stateVar.GetSingleObjNSCE();

            end

            t2 = cputime;
            tElapse = t2 - t1;

            if isempty(NSCE) || isempty(tElapse)
                NSCE = -9999;
                tElapse = 0;
            end

        end

    end

    methods (Access = protected)

        function cpObj = copyElement(obj)

            cpObj = <EMAIL>(obj);
        end

    end

    methods (Access = private)

        function ModelParInit(obj, x0, keywords)

            if nargin == 3
                obj.modelPar.ModelParReInitialize(x0, keywords);
                obj.basicVar.RunoffAndRoutePre(obj.globalVar.timeStepInM, ...
                    obj.modelPar.coeM, obj.modelPar.expM, obj.modelPar.coeR, obj.modelPar.coeS, ...
                    obj.basicVar.rowOutlet, obj.basicVar.colOutlet, obj.globalVar.hasRiverInterflow);
            else
                obj.modelPar.ModelParReInitialize();

                if obj.globalVar.output_runoff
                    [riverRow, riverCol] = obj.basicVar.GetStreamRowAndCol();
                    obj.basicVar.RunoffAndRoutePre(obj.globalVar.timeStepInM, ...
                        obj.modelPar.coeM, obj.modelPar.expM, obj.modelPar.coeR, obj.modelPar.coeS, ...
                        riverRow, riverCol, obj.globalVar.hasRiverInterflow);
                else
                    obj.basicVar.RunoffAndRoutePre(obj.globalVar.timeStepInM, ...
                        obj.modelPar.coeM, obj.modelPar.expM, obj.modelPar.coeR, obj.modelPar.coeS, ...
                        obj.stateVar.hydroSites.row, obj.stateVar.hydroSites.col, obj.globalVar.hasRiverInterflow);
                end

            end

            obj.countValid = sum(obj.stateVar.basinMask(obj.stateVar.basinMask));
            obj.WM = obj.modelPar.WM(obj.stateVar.basinMask);

            obj.modelPar.AfterModelParUpdate();
            obj.WMM = obj.modelPar.WMM(obj.stateVar.basinMask);
            obj.B = obj.modelPar.B(obj.stateVar.basinMask);
            obj.IM = obj.modelPar.IM(obj.stateVar.basinMask);
            obj.Ksat = obj.modelPar.Ksat(obj.stateVar.basinMask);
            obj.EAct = zeros(length(obj.B), 1);
            obj.excS = zeros(length(obj.B), 1);
            obj.excI = zeros(length(obj.B), 1);

            obj.KS = obj.modelPar.KS(obj.stateVar.basinMask);
            obj.KI = obj.modelPar.KI(obj.stateVar.basinMask);

            obj.gridArea = obj.basicVar.gridArea(obj.stateVar.basinMask);

            [obj.SIndexA, obj.uSIndexA, obj.SIndexAValid] = obj.ComputeRoutingIndices( ...
                obj.basicVar.SRowA(obj.stateVar.basinMask), ...
                obj.basicVar.SColA(obj.stateVar.basinMask));
            obj.SFracA = obj.basicVar.SFracA(obj.stateVar.basinMask);
            obj.SFracA = obj.SFracA(obj.SIndexAValid);
            obj.SgridAreaA = obj.gridArea(obj.SIndexAValid);

            [obj.SIndexB, obj.uSIndexB, obj.SIndexBValid] = obj.ComputeRoutingIndices( ...
                obj.basicVar.SRowB(obj.stateVar.basinMask), ...
                obj.basicVar.SColB(obj.stateVar.basinMask));
            obj.SFracB = obj.basicVar.SFracB(obj.stateVar.basinMask);
            obj.SFracB = obj.SFracB(obj.SIndexBValid);
            obj.SgridAreaB = obj.gridArea(obj.SIndexBValid);

            [obj.IIndexA, obj.uIIndexA, obj.IIndexAValid] = obj.ComputeRoutingIndices( ...
                obj.basicVar.IRowA(obj.stateVar.basinMask), ...
                obj.basicVar.IColA(obj.stateVar.basinMask));
            obj.IFracA = obj.basicVar.IFracA(obj.stateVar.basinMask);
            obj.IFracA = obj.IFracA(obj.IIndexAValid);
            obj.IgridAreaA = obj.gridArea(obj.IIndexAValid);

            [obj.IIndexB, obj.uIIndexB, obj.IIndexBValid] = obj.ComputeRoutingIndices( ...
                obj.basicVar.IRowB(obj.stateVar.basinMask), ...
                obj.basicVar.IColB(obj.stateVar.basinMask));
            obj.IFracB = obj.basicVar.IFracB(obj.stateVar.basinMask);
            obj.IFracB = obj.IFracB(obj.IIndexBValid);
            obj.IgridAreaB = obj.gridArea(obj.IIndexBValid);

            obj.RSPassedIndex = sub2ind(size(obj.stateVar.basinMask), ...
                obj.basicVar.RSPassedRow, obj.basicVar.RSPassedCol);
            obj.uRSPassedIndex = unique(obj.RSPassedIndex);
            obj.RSStartedIndex = sub2ind(size(obj.stateVar.basinMask), ...
                obj.basicVar.RSStartedRow, obj.basicVar.RSStartedCol);
            obj.RIPassedIndex = sub2ind(size(obj.stateVar.basinMask), ...
                obj.basicVar.RIPassedRow, obj.basicVar.RIPassedCol);
            obj.uRIPassedIndex = unique(obj.RIPassedIndex);
            obj.RIStartedIndex = sub2ind(size(obj.stateVar.basinMask), ...
                obj.basicVar.RIStartedRow, obj.basicVar.RIStartedCol);

        end

        function [XIndexY, uXIndexY, XIndexValidY] = ComputeRoutingIndices(obj, XRowY, XColY)

            [XIndexY, XIndexValidY] = obj.basicVar.sub2indInBasin(XRowY, XColY);

            XIndexY = XIndexY(XIndexValidY);
            uXIndexY = unique(XIndexY);

        end

        function DirectRunoffGen(obj)

            obj.W0 = obj.stateVar.W0(obj.stateVar.basinMask);
            WPrev = obj.W0;
            obj.PET = obj.stateVar.PET(obj.stateVar.basinMask);
            obj.rain = obj.stateVar.rain(obj.stateVar.basinMask);
            dW = zeros(obj.countValid, 1);
            PSoil = zeros(obj.countValid, 1);
            Qd = zeros(obj.countValid, 1);
            A = zeros(obj.countValid, 1);

            if obj.globalVar.feedback

                ThrouRain = obj.rain;
                ThrouRain(~obj.basicVar.stream(obj.basicVar.basinMask)) = ...
                    ThrouRain(~obj.basicVar.stream(obj.basicVar.basinMask)) + ...
                    obj.stateVar.SS0(~obj.basicVar.stream(obj.basicVar.basinMask));
                obj.stateVar.SS0(~obj.basicVar.stream(obj.basicVar.basinMask)) = 0;

            else
                ThrouRain = obj.rain;

            end

            indexOverRain = ThrouRain > obj.PET;
            indexDeficRain = ~indexOverRain;
            indexSoilUnsat = WPrev <= obj.WM;
            indexSoilSat = ~indexSoilUnsat;

            indexUnsatOverRain = logical(indexSoilUnsat .* indexOverRain);
            indexSatOvrRain = logical((indexSoilSat) .* indexOverRain);

            PSoil(indexOverRain) = (ThrouRain(indexOverRain) - obj.PET(indexOverRain)) .* (1.0 - obj.IM(indexOverRain));

            A(indexUnsatOverRain) = obj.WMM(indexUnsatOverRain) .* ...
                (1.0 - (1.0 - WPrev(indexUnsatOverRain) ./ obj.WM(indexUnsatOverRain)) .^ ...
                (1.0 ./ (1.0 + obj.B(indexUnsatOverRain))));

            Qd(indexSatOvrRain) = PSoil(indexSatOvrRain);

            Qd(indexUnsatOverRain) = PSoil(indexUnsatOverRain) - ...
                obj.WM(indexUnsatOverRain) + WPrev(indexUnsatOverRain);

            indexL = PSoil + A < obj.WMM;
            indexLightRain = logical(indexL .* indexUnsatOverRain);
            indexHeavyRain = logical(~indexL .* indexUnsatOverRain);

            Qd(indexLightRain) = Qd(indexLightRain) + ...
                obj.WM(indexLightRain) .* ...
                (1 - (A(indexLightRain) + PSoil(indexLightRain)) ./ obj.WMM(indexLightRain)) .^ ...
                (1 + obj.B(indexLightRain));
            Qd(Qd < 0) = 0;

            dW(indexDeficRain) = (obj.PET(indexDeficRain) - ThrouRain(indexDeficRain)) .* ...
                WPrev(indexDeficRain) ./ obj.WM(indexDeficRain);

            dW(indexDeficRain) = min(dW(indexDeficRain), WPrev(indexDeficRain));
            obj.W0(indexDeficRain) = WPrev(indexDeficRain) - dW(indexDeficRain);

            obj.W0(indexLightRain) = WPrev(indexLightRain) + PSoil(indexLightRain) - Qd(indexLightRain);

            obj.W0(indexSatOvrRain) = WPrev(indexSatOvrRain);

            obj.W0(indexHeavyRain) = obj.WM(indexHeavyRain);

            obj.excS(indexDeficRain) = 0;
            obj.excI(indexDeficRain) = 0;

            maxExcI = ((obj.W0(indexOverRain) + WPrev(indexOverRain)) / 2) .* (obj.Ksat(indexOverRain) * obj.globalVar.timeStepInM) ./ obj.WM(indexOverRain);

            obj.excI(indexOverRain) = min(maxExcI, Qd(indexOverRain));

            obj.excS(indexOverRain) = Qd(indexOverRain) - obj.excI(indexOverRain) + ...
                (ThrouRain(indexOverRain) - obj.PET(indexOverRain)) .* obj.IM(indexOverRain);

            obj.EAct(indexDeficRain) = WPrev(indexDeficRain) - obj.W0(indexDeficRain) + ThrouRain(indexDeficRain);
            obj.EAct(indexOverRain) = obj.PET(indexOverRain);
        end

        function WaterBudgetUpdate(obj)

            obj.SS0 = obj.stateVar.SS0(obj.stateVar.basinMask) + obj.excS;
            obj.SI0 = obj.stateVar.SI0(obj.stateVar.basinMask) + obj.excI;

            if ~obj.globalVar.hasRiverInterflow
                obj.stateVar.SS0(obj.stateVar.basinMask) = obj.SS0;
                obj.stateVar.SI0(obj.stateVar.basinMask) = obj.SI0;
                obj.stateVar.SS0(obj.basicVar.stream) = obj.stateVar.SS0(obj.basicVar.stream) + obj.stateVar.SI0(obj.basicVar.stream);
                obj.stateVar.SI0(obj.basicVar.stream) = 0;
                obj.SS0 = obj.stateVar.SS0(obj.stateVar.basinMask);
                obj.SI0 = obj.stateVar.SI0(obj.stateVar.basinMask);
            end

            obj.RS = obj.SS0 .* obj.KS;
            obj.SS0 = obj.SS0 - obj.RS;
            obj.RI = obj.SI0 .* obj.KI;
            obj.SI0 = obj.SI0 - obj.RI;

        end

        function StateVarUpdate(obj)
            obj.stateVar.SS0(obj.stateVar.basinMask) = obj.SS0;
            obj.stateVar.SI0(obj.stateVar.basinMask) = obj.SI0;
            obj.stateVar.excS(obj.stateVar.basinMask) = obj.excS;
            obj.stateVar.excI(obj.stateVar.basinMask) = obj.excI;
            obj.stateVar.RS(obj.stateVar.basinMask) = obj.RS;
            obj.stateVar.RI(obj.stateVar.basinMask) = obj.RI;

            obj.stateVar.EAct(obj.stateVar.basinMask) = obj.EAct;
            obj.stateVar.W0(obj.stateVar.basinMask) = obj.W0;

            obj.stateVar.pW0(obj.stateVar.basinMask) = obj.W0 ./ obj.WM * 100;
        end

        function DownstreamRoute(obj)

            obj.runoff = (obj.RS + obj.RI) / obj.globalVar.timeStepInM .* obj.gridArea / 3.6 * obj.rTimeUnit;
            obj.stateVar.runoff(obj.stateVar.basinMask) = obj.runoff;

            if ~isempty(obj.RSPassedIndex)
                dRunoffS = accumarray(obj.RSPassedIndex, ...
                    obj.stateVar.RS(obj.RSStartedIndex) .* obj.basicVar.gridArea(obj.RSStartedIndex) / (obj.globalVar.timeStepInM * 3.6 / obj.rTimeUnit), [], [], [], false);

                dRunoffS = dRunoffS(obj.uRSPassedIndex);

                obj.stateVar.runoff(obj.uRSPassedIndex) = obj.stateVar.runoff(obj.uRSPassedIndex) + dRunoffS;
            end

            if ~isempty(obj.RIPassedIndex)
                dRunoffI = accumarray(obj.RIPassedIndex, ...
                    obj.stateVar.RI(obj.RIStartedIndex) .* obj.basicVar.gridArea(obj.RIStartedIndex) / (obj.globalVar.timeStepInM * 3.6 / obj.rTimeUnit), [], [], [], false);
                dRunoffI = dRunoffI(obj.uRIPassedIndex);

                obj.stateVar.runoff(obj.uRIPassedIndex) = obj.stateVar.runoff(obj.uRIPassedIndex) + dRunoffI;
            end

            if ~isempty(obj.SIndexA)
                dSS0 = accumarray(obj.SIndexA, ...
                    obj.RS(obj.SIndexAValid) .* obj.SFracA .* obj.SgridAreaA, [], [], [], false);
                dSS0 = dSS0(obj.uSIndexA);

                obj.stateVar.SS0(obj.uSIndexA) = obj.stateVar.SS0(obj.uSIndexA) + ...
                    dSS0 ./ obj.basicVar.gridArea(obj.uSIndexA);
            end

            if ~isempty(obj.SIndexB)
                dSS0 = accumarray(obj.SIndexB, ...
                    obj.RS(obj.SIndexBValid) .* obj.SFracB .* obj.SgridAreaB, [], [], [], false);
                dSS0 = dSS0(obj.uSIndexB);

                obj.stateVar.SS0(obj.uSIndexB) = obj.stateVar.SS0(obj.uSIndexB) + ...
                    dSS0 ./ obj.basicVar.gridArea(obj.uSIndexB);
            end

            if ~isempty(obj.IIndexA)
                dSI0 = accumarray(obj.IIndexA, ...
                    obj.RI(obj.IIndexAValid) .* obj.IFracA .* obj.IgridAreaA, [], [], [], false);
                dSI0 = dSI0(obj.uIIndexA);

                obj.stateVar.SI0(obj.uIIndexA) = obj.stateVar.SI0(obj.uIIndexA) + ...
                    dSI0 ./ obj.basicVar.gridArea(obj.uIIndexA);
            end

            if ~isempty(obj.IIndexB)
                dSI0 = accumarray(obj.IIndexB, ...
                    obj.RI(obj.IIndexBValid) .* obj.IFracB .* obj.IgridAreaB, [], [], [], false);
                dSI0 = dSI0(obj.uIIndexB);

                obj.stateVar.SI0(obj.uIIndexB) = obj.stateVar.SI0(obj.uIIndexB) + ...
                    dSI0 ./ obj.basicVar.gridArea(obj.uIIndexB);
            end

        end

    end

end
