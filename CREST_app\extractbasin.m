function[mask,Dgrid,outlet_row,outlet_col,user_outlet_row,user_outlet_col,rel_err,new_est_darea]=extractbasin(facc,fdir,row,col,cellsize,tolerance,max_radius)





























    basin_area=[];











    new_row=round(row);new_col=round(col);
    user_outlet_row=round(row);user_outlet_col=round(col);



    est_darea=facc(new_row,new_col)*((cellsize/1000)^2);


    if(isempty(basin_area)==0)
        darea=basin_area;


        rel_err=abs((darea-est_darea)/darea)*100;
        new_est_darea=est_darea;
    else
        darea=[];
        new_est_darea=est_darea;
        rel_err=0;
    end

    radius_distance=1;
    while(rel_err>tolerance)

        search_pixels_rows=user_outlet_row-radius_distance:user_outlet_row+radius_distance;
        search_pixels_cols=user_outlet_col-radius_distance:user_outlet_col+radius_distance;

        search_pixels_rows=search_pixels_rows(search_pixels_rows>0);
        search_pixels_cols=search_pixels_cols(search_pixels_cols>0);
        max_row=max(search_pixels_rows);
        max_col=max(search_pixels_cols);

        if(radius_distance*(cellsize/1000)>(max_radius/1000)||max_row>size(facc,1)||max_col>size(facc,2))


            fprintf('\n\nWARNING: Could not find an accurate location within allowed domain for outlet at:\nlat: %g lon: %g with known drainage area %g km^2',lat,lon,darea);
            fprintf('\nForcing location at row: %g col: %g with drainage area %g km^2 and %g percent error.\n\n',new_row,new_col,new_est_darea,rel_err);
            break;
        end

        radius_search=facc(search_pixels_rows,search_pixels_cols).*((cellsize/1000)^2);
        radius_search_diff=abs(radius_search-darea);
        [new_i,new_j]=find(radius_search_diff==min(radius_search_diff(:)));
        new_row=(new_i-radius_distance-1)+user_outlet_row;
        new_col=(new_j-radius_distance-1)+user_outlet_col;
        new_row=new_row(1);
        new_col=new_col(1);

        new_est_darea=facc(new_row,new_col)*((cellsize/1000)^2);


        rel_err=abs((darea-new_est_darea)/darea)*100;

        radius_distance=radius_distance+1;
    end


    outlet_row=new_row;
    outlet_col=new_col;
    new_est_darea=facc(outlet_row,outlet_col)*((cellsize/1000)^2);



    hlen=nan(size(fdir));
    hlen(isnan(fdir)==0)=cellsize*sqrt(2);
    hlen(fdir==1|fdir==4|fdir==16|fdir==64)=cellsize;



    mask=zeros(size(fdir));
    Lgrid=nan(size(facc));
    Dgrid=nan(size(facc));
    drain_group=[];
    new_drain_group=[];
    new_level_length=[];
    level_length=[];


    mask(outlet_row,outlet_col)=1;


    n_contributing_cells=facc(outlet_row,outlet_col);



    fdircodes=[2,4,8,16,32,64,128,1];




    surroundings=[new_row-1,new_col-1;
    new_row-1,new_col;
    new_row-1,new_col+1;
    new_row,new_col+1;
    new_row+1,new_col+1;
    new_row+1,new_col;
    new_row+1,new_col-1;
    new_row,new_col-1];


    [surr_r]=find(min(surroundings,[],2)>0&surroundings(:,1)<=size(fdir,1)&surroundings(:,2)<=size(fdir,2));
    surroundings=surroundings(surr_r,:);
    fdircodes_surr=fdircodes(surr_r);



    n_accounted_cells=0;
    cont=0;
    dist_level=1;
    for neig=1:size(surroundings,1)
        if(fdir(surroundings(neig,1),surroundings(neig,2))==fdircodes_surr(neig))
            cont=cont+1;
            n_accounted_cells=n_accounted_cells+1;
            drain_group(cont,:)=surroundings(neig,:);
            level_length(cont)=hlen(surroundings(neig,1),surroundings(neig,2));
            mask(surroundings(neig,1),surroundings(neig,2))=1;
            Lgrid(surroundings(neig,1),surroundings(neig,2))=dist_level;
            Dgrid(surroundings(neig,1),surroundings(neig,2))=hlen(surroundings(neig,1),surroundings(neig,2));
        end
    end


    while(n_accounted_cells<n_contributing_cells)
        cont=0;

        dist_level=dist_level+1;
        for cells=1:size(drain_group,1)
            new_row=drain_group(cells,1);
            new_col=drain_group(cells,2);



            surroundings=[new_row-1,new_col-1;
            new_row-1,new_col;
            new_row-1,new_col+1;
            new_row,new_col+1;
            new_row+1,new_col+1;
            new_row+1,new_col;
            new_row+1,new_col-1;
            new_row,new_col-1];


            [surr_r]=find(min(surroundings,[],2)>0&surroundings(:,1)<=size(fdir,1)&surroundings(:,2)<=size(fdir,2));
            surroundings=surroundings(surr_r,:);
            fdircodes_surr=fdircodes(surr_r);


            for neig=1:size(surroundings,1)
                if(fdir(surroundings(neig,1),surroundings(neig,2))==fdircodes_surr(neig))
                    cont=cont+1;
                    n_accounted_cells=n_accounted_cells+1;
                    new_drain_group(cont,:)=surroundings(neig,:);
                    mask(surroundings(neig,1),surroundings(neig,2))=1;
                    Lgrid(surroundings(neig,1),surroundings(neig,2))=dist_level;
                    Dgrid(surroundings(neig,1),surroundings(neig,2))=hlen(surroundings(neig,1),surroundings(neig,2))+level_length(cells);
                    new_level_length(cont)=Dgrid(surroundings(neig,1),surroundings(neig,2));
                end
            end
        end

        drain_group=new_drain_group;
        level_length=new_level_length;
        new_drain_group=[];
        new_level_length=[];
    end
end