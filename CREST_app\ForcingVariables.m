classdef ForcingVariables<RasterVariables

    properties


        PET;
        rain;
        LAI;
        lat;lon;
        row;col;
        dateStart;
        dateEnd;
        timeStep;
        timeFormat;
        timeMark;
        nt;
        iPeriod;
        nPeriod;
    end
    properties(SetAccess=private,GetAccess=public)
        dateCur;


        rainTsRatio;PETTsRatio;LAITsRatio;

        dirIntPET;dirIntRain;dirIntLAI;

        dirExtPET;dirExtRain;dirExtLAI;


        extRain;extPET;extLAI;

        fmtRain;fmtPET;fmtLAI;

        dateConvRain;dateConvPET;
        useLAI;

    end
    properties(Access=private)
        dateLAISto;dateRainSto;datePETSto;
        intervalRain;intervalPET;intervalLAI;
        dateLAI0;dateRain0;datePET0;

        decompBeforeSrc;
        decompBeforeDst;
        pathSplitor;
    end
    methods
        function obj=ForcingVariables(useLAI,...
            basinMask,...
            dateStart,dateEnd,timeStep,timeFormat,...
            timeMark,...
            geoTransBasic,spatialRefBasic,...
            dirExtPET,dirExtRain,dirExtLAI,...
            dateFmtPET,dateFmtRain,dateFmtLAI,...
            dateConvPET,dateConvRain,...
            extensionPET,extensionRain,extensionLAI,...
            PETTsR,RainTsR,LAITsR,...
            startPET,startRain,startLAI,...
            intervalPET,intervalRain,intervalLAI,...
            dirIntPET,dirIntRain,dirIntLAI,...
            decompBeforeSrc,decompBeforeDst,OS)
            obj.useLAI=useLAI;
            obj.decompBeforeSrc=decompBeforeSrc;
            obj.decompBeforeDst=decompBeforeDst;
            obj.pathSplitor='/';
            switch OS
            case 'linux'
                obj.pathSplitor='/';
            case 'windows'
                obj.pathSplitor='\';
            end
            obj.geoTrans=geoTransBasic;obj.spatialRef=spatialRefBasic;
            obj.bGCS=IsGeographic(obj.spatialRef,obj.geoTrans);
            obj.basinMask=basinMask;
            obj.timeFormat=timeFormat;
            obj.timeMark=timeMark;
            obj.nPeriod=length(dateEnd);
            obj.fmtRain=dateFmtRain;obj.fmtPET=dateFmtPET;obj.fmtLAI=dateFmtLAI;
            obj.extRain=extensionRain;obj.extPET=extensionPET;obj.extLAI=extensionLAI;
            obj.rainTsRatio=RainTsR;obj.PETTsRatio=PETTsR;obj.LAITsRatio=LAITsR;
            obj.dirExtPET=dirExtPET;obj.dirExtRain=dirExtRain;obj.dirExtLAI=dirExtLAI;
            obj.dirIntPET=dirIntPET;obj.dirIntRain=dirIntRain;obj.dirIntLAI=dirIntLAI;
            obj.dateConvPET=dateConvPET;obj.dateConvRain=dateConvRain;
            obj.dateStart=dateStart;obj.dateEnd=dateEnd;obj.timeStep=timeStep;
            obj.intervalRain=intervalRain;obj.intervalPET=intervalPET;obj.intervalLAI=intervalLAI;
            obj.datePET0=ForcingVariables.InitializeStartForcTime(startPET,obj.intervalPET,obj.dateConvPET);
            obj.dateRain0=ForcingVariables.InitializeStartForcTime(startRain,obj.intervalRain,obj.dateConvRain);
            obj.dateLAI0=ForcingVariables.InitializeStartForcTime(startLAI,obj.intervalRain,obj.dateConvRain);
            obj.InitGrids();
            obj.iPeriod=0;
        end
        function bEnd=reset(obj,mode,isReload)
            bEnd=false;
            obj.nt=0;
            if isReload
                obj.iPeriod=obj.iPeriod+1;
            else
                obj.iPeriod=1;
            end
            if obj.iPeriod>obj.nPeriod
                bEnd=true;
                obj.iPeriod=0;
                return;
            end
            obj.dateCur=obj.dateStart(obj.iPeriod);
            if isReload
                obj.dateRainSto=obj.dateCur;
                obj.datePETSto=obj.dateCur;
            else
                obj.dateLAISto=obj.dateLAI0-obj.intervalLAI;
                obj.dateRainSto=obj.dateRain0-obj.intervalRain;
                obj.datePETSto=obj.datePET0-obj.intervalPET;
            end
            obj.ReadIntForcing(mode);
        end
        function res=MoveNext(obj,mode)
            obj.nt=obj.nt+1;
            if obj.dateCur<obj.dateEnd(obj.iPeriod)
                res=true;
            else
                res=false;
                return;
            end
            obj.dateCur=obj.dateStart(obj.iPeriod)+obj.nt*obj.timeStep;
            obj.ReadIntForcing(mode);
        end
        function ReadIntForcing(obj,mode)
            [rows,cols]=size(obj.basinMask);
            rainNameInt=ForcingVariables.GenerateIntForcingFileName(obj.dirIntRain,obj.timeFormat,obj.dateCur);
            dateToReadRain=ForcingVariables.fileDateToUpdate(obj.dateCur,obj.dateRainSto,[],obj.intervalRain);
            if exist(rainNameInt,'file')==2
                S=load(rainNameInt);
                obj.rain=S.srain;
                clear S
            else
                if abs(dateToReadRain-obj.dateCur)>obj.intervalRain
                    warning('rainfall setting error, please check the control file');
                end
                if dateToReadRain~=obj.dateRainSto
                    rainNameExt=ForcingVariables.GenerateExtForcingFileName(dateToReadRain,obj.intervalRain,obj.fmtRain,obj.dateConvRain,obj.dirExtRain,obj.extRain);
                    if exist(rainNameExt,'file')==2
                        [forcRas,~,~]=ForcingVariables.ReadProjectedRaster(rainNameExt,rows,cols,obj.geoTrans,obj.spatialRef,...
                        obj.decompBeforeSrc,obj.decompBeforeDst,obj.pathSplitor);
                        forcRas(forcRas<0)=0;
                        obj.rain(obj.basinMask)=forcRas(obj.basinMask)/obj.rainTsRatio;

                    else
                        switch mode
                        case 'simu'
                            warning(strcat('missing rainfall data on  ',datestr(obj.dateCur)));
                        end
                    end
                end
                srain=obj.rain;
                save(rainNameInt,'srain');
                clear srain
            end
            obj.dateRainSto=dateToReadRain;

            PETNameInt=ForcingVariables.GenerateIntForcingFileName(obj.dirIntPET,obj.timeFormat,obj.dateCur);
            dateToReadPET=ForcingVariables.fileDateToUpdate(obj.dateCur,obj.datePETSto,[],obj.intervalPET);
            if exist(PETNameInt,'file')==2
                S=load(PETNameInt);
                obj.PET=S.sPET;
                clear S
            else
                if abs(dateToReadPET-obj.dateCur)>obj.intervalPET
                    warning('PET setting error, please check the control file');
                end
                if dateToReadPET~=obj.datePETSto
                    PETNameExt=ForcingVariables.GenerateExtForcingFileName(dateToReadPET,obj.intervalPET,obj.fmtPET,obj.dateConvPET,obj.dirExtPET,obj.extPET);
                    if exist(PETNameExt,'file')==2
                        forcRas=ForcingVariables.ReadProjectedRaster(PETNameExt,rows,cols,obj.geoTrans,obj.spatialRef,...
                        obj.decompBeforeSrc,obj.decompBeforeDst,obj.pathSplitor);
                        forcRas(forcRas<0)=0;
                        obj.PET(obj.basinMask)=forcRas(obj.basinMask)/obj.PETTsRatio;

                    else
                        switch mode
                        case 'simu'
                            warning(strcat('missing PET data on ',datestr(obj.dateCur)));
                        end
                    end
                end
                sPET=obj.PET;
                save(PETNameInt,'sPET');
                clear sPET
            end
            obj.datePETSto=dateToReadPET;
            if obj.useLAI
                LAINameInt=ForcingVariables.GenerateIntForcingFileName(obj.dirIntLAI,obj.timeFormat,obj.dateCur);
                dateToReadLAI=ForcingVariables.fileDateToUpdate(obj.dateCur,obj.dateLAI,obj.intervalLAI);
                if exist(LAINameInt,'file')==2
                    S=load(LAINameInt);
                    obj.LAI=S.sLAI;
                    clear S
                    obj.dateLAI=obj.dateCur;
                else
                    if abs(dateToReadLAI-obj.dateCur)>obj.intervalLAI
                        warning('LAI setting error, please check the control file');
                    end
                    if dateToReadLAI~=obj.dateLAISto
                        LAINameExt=ForcingVariables.GenerateExtForcingFileName(dateToRead,obj.timeStep,obj.fmtLAI,obj.dirExtLAI,obj.extLAI);
                        if exist(LAINameExt,'file')==2
                            [forcRas,~,~]=ForcingVariables.ReadProjectedRaster(LAINameExt,rows,cols,obj.geoTrans,obj.spatialRef,obj.decompBeforeSrc,obj.decompBeforeDst,obj.pathSplitor);
                            forcRas(forcRas<0)=0;
                            obj.LAI(obj.basinMask)=forcRas(obj.basinMask)/obj.LAITsRatio;
                        else
                            switch mode
                            case 'simu'
                                warning(strcat('missing LAI data on ',datestr(obj.dateCur)));
                            end
                        end
                    end
                    sLAI=obj.LAI;
                    save(LAINameInt,'sLAI');
                    clear sLAI
                end
                obj.dateLAISto=dateToReadLAI;
            end
        end
    end
    methods(Access=private)
        function InitGrids(obj)
            [rows,columns]=size(obj.basinMask);
            [obj.col,obj.row]=meshgrid(1:columns,1:rows);
            obj.row(~obj.basinMask)=NaN;
            obj.col(~obj.basinMask)=NaN;
            obj.lat=obj.Initialize();
            obj.lon=obj.Initialize();
            [obj.lat(obj.basinMask),obj.lon(obj.basinMask)]=RowCol2Proj(obj.geoTrans,obj.row(obj.basinMask),obj.col(obj.basinMask));
            obj.rain=obj.Initialize();
            obj.PET=obj.Initialize();
            if obj.useLAI
                obj.LAI=obj.Initialize();
            end
        end
    end
    methods(Static)
        function fileState=GenerateFileNames(headForcing)
            fileState=headForcing;
        end
        function dateToRead=fileDateToUpdate(dateCur,dateSto,dateReset,dateInterval)

            [yCur,~,~]=datevec(dateCur);
            dateNext=dateSto+dateInterval;
            if~isempty(dateReset)
                [~,mReset,dReset,HReset,MReset,SReset]=datevec(dateReset);
                dateReset=datenum(yCur,mReset,dReset,HReset,MReset,SReset);
                if dateNext-dateReset<dateInterval
                    dateNext=dateReset;
                end
            end
            dateDiff1=dateCur-dateSto;
            dateDiff2=dateNext-dateCur;
            if dateDiff1<dateDiff2
                dateToRead=dateSto;
            else
                dateToRead=dateNext;
            end
        end
        function forcStart=InitializeStartForcTime(forcStart,forcStep,convForc)
            switch convForc
            case 'Begin'
                forcStart=forcStart+forcStep/2;
            case 'End'
                forcStart=forcStart-forcStep/2;
            end
        end
        function forcNameExt=GenerateExtForcingFileName(date,timeStep,fmtForc,convForc,dirForc,extForc)
            switch convForc
            case 'Begin'
                date=date-timeStep/2;
            case 'End'
                date=date+timeStep/2;
            end
            switch fmtForc,
            case 'yyyy\DOY'
                [Y,~,~,~,~,~]=datevec(date);
                doy=date-datenum(Y,1,1)+1;
                doy=num2str(doy,'%03d');
                forcNameExt=strcat(dirForc,datestr(date,'yyyy'),'/',num2str(doy),extForc);
            otherwise
                strDate=datestr(date,fmtForc);
                forcNameExt=strcat(dirForc,strDate,extForc);
            end

        end
        function forcNameInt=GenerateIntForcingFileName(dirIntForc,fmtForc,date)
            strDate=datestr(date,fmtForc);
            forcNameInt=strcat(dirIntForc,strDate,'.mat');
        end
        function[raster,geoTransForc,spatialRefForc]=ReadProjectedRaster(fileName,...
            rowsBasic,colsBasic,geoTransBasic,spatialRefBasic,...
            decompBeforeSrc,decompAfterSrc,pathSplitor)
            [dir,name,ext]=fileparts(fileName);


            cmdDecomp=['!',decompBeforeSrc,' '];
            if strcmpi(ext,'.gz')||strcmpi(ext,'.z')||strcmpi(ext,'.tar')


                fileDecomp=[dir,pathSplitor,name];


                if isempty(decompAfterSrc)
                    decompAfterSrc=' ';
                end
                if strcmpi(pathSplitor,'\')
                    strcmd=[cmdDecomp,'"',fileName,'"',decompAfterSrc,'"',dir,'"'];
                else
                    strcmd=[cmdDecomp,'"',fileName,'"',decompAfterSrc,'"',fileDecomp,'"'];
                end
                eval(strcmd);
            else
                fileDecomp=fileName;
            end
            clippedAndResampledFile=[dir,'/','temp.tif'];
            outFormat='GTiff';
            ResampleAndClip(geoTransBasic,spatialRefBasic,colsBasic,rowsBasic,fileDecomp,clippedAndResampledFile,outFormat);
            [raster,geoTransForc,spatialRefForc]=ReadRaster(clippedAndResampledFile);
            deleteRaster(clippedAndResampledFile,outFormat);
            if strcmpi(ext,'.gz')||strcmpi(ext,'.z')||strcmpi(ext,'.tar')

                delete(fileDecomp);
            end
        end
    end
end