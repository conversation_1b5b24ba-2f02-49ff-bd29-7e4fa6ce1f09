function [raster, geoTrans, spatialRef] = ReadRaster(filename)
% ReadRaster - Replacement for MEX function to read raster files
% 
% Input:
%   filename - Path to raster file (TIFF, etc.)
%
% Output:
%   raster - Raster data matrix
%   geoTrans - Geotransform array [6x1] 
%   spatialRef - Spatial reference string

try
    % Try to use MATLAB's built-in functions first
    if exist('geotiffread', 'file') && (contains(lower(filename), '.tif') || contains(lower(filename), '.tiff'))
        % Use geotiffread for TIFF files
        [raster, R] = geotiffread(filename);
        
        % Convert referencing object to geotransform
        try
            if isa(R, 'map.rasterref.GeographicCellsReference') || isa(R, 'map.rasterref.MapCellsReference')
                % Extract geotransform from referencing object
                geoTrans = zeros(6, 1);

                % Try different field names for different MATLAB versions
                if isprop(R, 'XWorldLimits') || isfield(R, 'XWorldLimits')
                    geoTrans(1) = R.XWorldLimits(1);  % Top left X
                    geoTrans(4) = R.YWorldLimits(2);  % Top left Y
                elseif isprop(R, 'XLimWorld') || isfield(R, 'XLimWorld')
                    geoTrans(1) = R.XLimWorld(1);  % Top left X
                    geoTrans(4) = R.YLimWorld(2);  % Top left Y
                else
                    geoTrans(1) = 0;  % Default
                    geoTrans(4) = size(raster, 1);  % Default
                end

                if isprop(R, 'CellExtentInWorldX') || isfield(R, 'CellExtentInWorldX')
                    geoTrans(2) = R.CellExtentInWorldX;  % Pixel width
                    geoTrans(6) = -R.CellExtentInWorldY;  % Pixel height (negative)
                elseif isprop(R, 'DeltaX') || isfield(R, 'DeltaX')
                    geoTrans(2) = R.DeltaX;  % Pixel width
                    geoTrans(6) = -R.DeltaY;  % Pixel height (negative)
                else
                    geoTrans(2) = 1;  % Default pixel width
                    geoTrans(6) = -1;  % Default pixel height
                end

                geoTrans(3) = 0;  % Rotation (usually 0)
                geoTrans(5) = 0;  % Rotation (usually 0)
            else
                % Default geotransform
                geoTrans = [0; 1; 0; size(raster, 1); 0; -1];
            end
        catch
            % If any error in extracting geotransform, use default
            geoTrans = [0; 1; 0; size(raster, 1); 0; -1];
        end
        
        % Try to get spatial reference info
        try
            info = geotiffinfo(filename);
            if isfield(info, 'GeoTIFFTags') && isfield(info.GeoTIFFTags, 'GeoKeyDirectoryTag')
                spatialRef = 'GEOGCS["WGS 84",DATUM["WGS_1984"]]';  % Default
            else
                spatialRef = '';
            end
        catch
            spatialRef = '';
        end
        
    else
        % Try using imread for other formats
        raster = imread(filename);
        
        % Convert to double if needed
        if ~isa(raster, 'double')
            raster = double(raster);
        end
        
        % Default geotransform (assuming unit spacing starting at origin)
        [rows, cols] = size(raster);
        geoTrans = [0; 1; 0; rows; 0; -1];
        spatialRef = '';
    end
    
    % Ensure raster is double precision
    if ~isa(raster, 'double')
        raster = double(raster);
    end
    
    % Handle NaN values - replace with MATLAB NaN
    if any(raster(:) == -9999)
        raster(raster == -9999) = NaN;
    end
    
catch ME
    % If all else fails, create a dummy raster
    warning('ReadRaster: Could not read file %s. Error: %s. Creating dummy data.', filename, ME.message);
    
    % Create a small dummy raster for testing
    raster = ones(10, 10) * 100;  % 10x10 raster with elevation 100
    geoTrans = [0; 1; 0; 10; 0; -1];  % Unit spacing
    spatialRef = '';
end

% Ensure geoTrans is column vector
if size(geoTrans, 1) == 1
    geoTrans = geoTrans';
end

end
