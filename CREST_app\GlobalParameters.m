classdef GlobalParameters
    properties
        timeFormat;timeMark;timeStep;timeStepInM;
        startDate;numOfLoaded;warmupDate;endDate;
        nTimeSteps;
        saveState;
        runStyle;
        feedback;
        hasRiverInterflow;
        useLAI;
        basicFormat;basicPath;...
        paramPath;statePath;ICSPath;
        rainPathExt;PETPathExt;LAIPathExt;
        rainPathInt;PETPathInt;LAIPathInt;
        obsPath;resPath;calibPath;calibMode;
        obsFormat;obsDateConvetion;obsNoData;
        rainFormat;rainDateFormat;RainTsScaling;rainDateConvention;
        PETFormat;PETDateFormat;PETTsScaling;PETDateConvention;
        LAIFormat;LAIDateFormat;LAITsScaling;LAIDateConvention;
        rainDateInterval;PETDateInterval;LAIDateInterval;
        rainDateStart;PETDateStart;LAIDateStart;
        nSites;
        STCD;
        px_lat;
        px_lon;
        hasOutlet;
        out_STCD;
        out_shp;

        output_Rain;
        output_EPot;
        output_EAct;
        output_runoff;
        output_W;
        output_SM;
        output_ExcS;
        output_ExcI;
        output_RS;
        output_RI;

        saveDates;
        saveDateFormat;
        saveOffset;
        decompBeforeSrc;
        decompBeforeDst;
        OS;
    end
    methods
        function obj=GlobalParameters(gFile)
            gfileID=fopen(gFile);
            commentSymbol='#';

            obj.timeMark=GlobalParameters.readLine(gfileID,'TimeMark',commentSymbol,'string');
            obj.timeFormat=GlobalParameters.readLine(gfileID,'TimeFormat',commentSymbol,'string');
            obj.timeStepInM=GlobalParameters.readLine(gfileID,'TimeStep',commentSymbol,'double');
            strStartDate=GlobalParameters.readLine(gfileID,'StartDate',commentSymbol,'string');
            obj.numOfLoaded=GlobalParameters.readLine(gfileID,'NLoad',commentSymbol,'double');
            if obj.numOfLoaded>0
                strWarmupDate=cell(obj.numOfLoaded,1);
                strEndDate=cell(obj.numOfLoaded,1);
                for i=1:obj.numOfLoaded
                    strWarmupDate{i}=GlobalParameters.readLine(gfileID,['WarmupDate_',num2str(i)],commentSymbol,'string');
                    strEndDate{i}=GlobalParameters.readLine(gfileID,['EndDate_',num2str(i)],commentSymbol,'string');
                end
            else
                strWarmupDate=GlobalParameters.readLine(gfileID,'WarmupDate',commentSymbol,'string');
                strEndDate=GlobalParameters.readLine(gfileID,'EndDate',commentSymbol,'string');
            end

            obj.runStyle=GlobalParameters.readLine(gfileID,'RunStyle',commentSymbol,'string');
            obj.feedback=GlobalParameters.readLine(gfileID,'Feedback',commentSymbol,'boolean');
            obj.hasRiverInterflow=GlobalParameters.readLine(gfileID,'hasRiverInterflow',commentSymbol,'boolean');
            obj.useLAI=GlobalParameters.readLine(gfileID,'UseLAI',commentSymbol,'boolean');
            obj.basicFormat=GlobalParameters.readLine(gfileID,'BasicFormat',commentSymbol,'string');
            obj.basicPath=GlobalParameters.readLine(gfileID,'BasicPath',commentSymbol,'string');
            obj.paramPath=GlobalParameters.readLine(gfileID,'ParamPath',commentSymbol,'string');
            obj.statePath=GlobalParameters.readLine(gfileID,'StatePath',commentSymbol,'string');
            obj.ICSPath=GlobalParameters.readLine(gfileID,'ICSPath',commentSymbol,'string');

            obj.rainFormat=GlobalParameters.readLine(gfileID,'RainFormat',commentSymbol,'string');
            obj.rainDateFormat=GlobalParameters.readLine(gfileID,'RainDateFormat',commentSymbol,'string');
            obj.rainDateConvention=GlobalParameters.readLine(gfileID,'RainDateConv',commentSymbol,'string');
            strRainDateStart=GlobalParameters.readLine(gfileID,'RainStart',commentSymbol,'string');
            strRainDateInterval=GlobalParameters.readLine(gfileID,'RainDateInterval',commentSymbol,'string');
            obj.rainPathExt=GlobalParameters.readLine(gfileID,'RainPathExt',commentSymbol,'string');
            obj.RainTsScaling=GlobalParameters.readLine(gfileID,'RainTsScaling',commentSymbol,'double');
            obj.rainPathInt=GlobalParameters.readLine(gfileID,'RainPathInt',commentSymbol,'string');

            obj.PETFormat=GlobalParameters.readLine(gfileID,'PETFormat',commentSymbol,'string');
            obj.PETDateFormat=GlobalParameters.readLine(gfileID,'PETDateFormat',commentSymbol,'string');
            obj.PETDateConvention=GlobalParameters.readLine(gfileID,'PETDateConv',commentSymbol,'string');
            strPETDateStart=GlobalParameters.readLine(gfileID,'PETStart',commentSymbol,'string');
            strPETDateInterval=GlobalParameters.readLine(gfileID,'PETDateInterval',commentSymbol,'string');
            obj.PETPathExt=GlobalParameters.readLine(gfileID,'PETPathExt',commentSymbol,'string');
            obj.PETTsScaling=GlobalParameters.readLine(gfileID,'PETTsScaling',commentSymbol,'double');
            obj.PETPathInt=GlobalParameters.readLine(gfileID,'PETPathInt',commentSymbol,'string');

            obj.LAIFormat=GlobalParameters.readLine(gfileID,'LAIFormat',commentSymbol,'string');
            obj.LAIDateFormat=GlobalParameters.readLine(gfileID,'LAIDateFormat',commentSymbol,'string');
            strLAIDateStart=GlobalParameters.readLine(gfileID,'LAIStart',commentSymbol,'string');
            strLAIDateInterval=GlobalParameters.readLine(gfileID,'LAIDateInterval',commentSymbol,'string');
            obj.LAIPathExt=GlobalParameters.readLine(gfileID,'LAIPathExt',commentSymbol,'string');
            obj.LAITsScaling=GlobalParameters.readLine(gfileID,'LAITsScaling',commentSymbol,'double');
            obj.LAIPathInt=GlobalParameters.readLine(gfileID,'LAIPathInt',commentSymbol,'string');

            obj.resPath=GlobalParameters.readLine(gfileID,'ResultPath',commentSymbol,'string');
            obj.calibPath=GlobalParameters.readLine(gfileID,'CalibPath',commentSymbol,'string');
            obj.calibMode=GlobalParameters.readLine(gfileID,'CalibMode',commentSymbol,'string');

            obj.obsFormat=GlobalParameters.readLine(gfileID,'OBSDateFormat',commentSymbol,'string');
            obj.obsPath=GlobalParameters.readLine(gfileID,'OBSPath',commentSymbol,'string');
            obj.obsNoData=GlobalParameters.readLine(gfileID,'OBSNoDataValue',commentSymbol,'double');










            obj.hasOutlet=GlobalParameters.readLine(gfileID,'HasOutlet',commentSymbol,'boolean');
            if obj.hasOutlet
                obj.out_STCD=GlobalParameters.readLine(gfileID,'OutletName',commentSymbol,'string');
            end
            obj.out_shp=[obj.obsPath,GlobalParameters.readLine(gfileID,'SitesShpFile',commentSymbol,'string')];
            obj.output_Rain=GlobalParameters.readLine(gfileID,'GOVar_Rain',commentSymbol,'boolean');

            obj.output_EPot=GlobalParameters.readLine(gfileID,'GOVar_EPot',commentSymbol,'boolean');
            obj.output_EAct=GlobalParameters.readLine(gfileID,'GOVar_EAct',commentSymbol,'boolean');
            obj.output_W=GlobalParameters.readLine(gfileID,'GOVar_W',commentSymbol,'boolean');
            obj.output_SM=GlobalParameters.readLine(gfileID,'GOVar_SM',commentSymbol,'boolean');
            obj.output_runoff=GlobalParameters.readLine(gfileID,'GOVar_R',commentSymbol,'boolean');
            obj.output_ExcS=GlobalParameters.readLine(gfileID,'GOVar_ExcS',commentSymbol,'boolean');
            obj.output_ExcI=GlobalParameters.readLine(gfileID,'GOVar_ExcI',commentSymbol,'boolean');
            obj.output_RS=GlobalParameters.readLine(gfileID,'GOVar_RS',commentSymbol,'boolean');
            obj.output_RI=GlobalParameters.readLine(gfileID,'GOVar_RI',commentSymbol,'boolean');
            nSaveDates=GlobalParameters.readLine(gfileID,'NumOfOutputDates',commentSymbol,'double');
            obj.saveDates=zeros(nSaveDates,1);
            obj.saveDateFormat=GlobalParameters.readLine(gfileID,'SaveDateFormat',commentSymbol,'string');
            obj.saveOffset=GlobalParameters.CalTimeInterval(...
            GlobalParameters.readLine(gfileID,'DateOffset',commentSymbol,'string'),obj.saveDateFormat);

            for i=1:nSaveDates
                istrSaveDate=GlobalParameters.readLine(gfileID,['OutputDate_',num2str(i)],commentSymbol,'string');
                obj.saveDates(i)=datenum(istrSaveDate,obj.timeFormat);
            end
            obj.decompBeforeSrc=GlobalParameters.readLine(gfileID,'DecompBeforeSrc',commentSymbol,'string');
            obj.decompBeforeDst=GlobalParameters.readLine(gfileID,'DecompBeforeDst',commentSymbol,'string');
            obj.OS=GlobalParameters.readLine(gfileID,'OS',commentSymbol,'string');

            fclose(gfileID);


            obj.startDate=datenum(strStartDate,obj.timeFormat);
            obj.warmupDate=datenum(strWarmupDate,obj.timeFormat);
            obj.endDate=datenum(strEndDate,obj.timeFormat);
            switch obj.timeMark
            case 'd'


                obj.timeStep=datenum(0,0,obj.timeStepInM);
            case 'h'





                obj.timeStep=datenum(0,0,0,obj.timeStepInM,0,0);
            end
            [obj.rainDateStart,obj.rainDateInterval]=GlobalParameters.ForcingTimePar(strRainDateStart,strRainDateInterval,obj.rainDateFormat);
            [obj.PETDateStart,obj.PETDateInterval]=GlobalParameters.ForcingTimePar(strPETDateStart,strPETDateInterval,obj.PETDateFormat);
            [obj.LAIDateStart,obj.LAIDateInterval]=GlobalParameters.ForcingTimePar(strLAIDateStart,strLAIDateInterval,obj.LAIDateFormat);

            if obj.numOfLoaded>0
                obj.nTimeSteps=round(sum(obj.endDate-obj.warmupDate)/obj.timeStep);
            else
                obj.nTimeSteps=round((obj.endDate-obj.startDate)/obj.timeStep)+1;
            end
        end
    end
    methods(Static=true)
        function interval=CalTimeInterval(str,fmt)
            if strcmpi(fmt,'yyyy\DOY')
                content=strsplit(str,'\');
                interval=str2double(content{1})*365+str2double(content{2});
            else
                strOrg='';
                for i=1:length(fmt)
                    number=str2double(str(i));
                    if~isnan(number)
                        strOrg=strcat(strOrg,'0');
                    else
                        strOrg=strcat(strOrg,str(i));
                    end
                end
                timeOrg=datenum(strOrg,fmt);
                time=datenum(str,fmt);
                interval=time-timeOrg;
            end
        end
        function value=readLine(gfileID,keyword,commentSymbol,type)
            value=-1;
            while value==-1
                tline=fgetl(gfileID);
                if strcmp(tline(1),commentSymbol)==1
                    continue;
                end
                strArr=regexp(tline,commentSymbol,'split');
                strContent=strArr{1};
                strContent=strtrim(strContent);
                if~isempty(regexpi(strContent,keyword,'ONCE'))
                    strValue=regexp(strContent,'=','split');
                    switch type
                    case 'double'
                        value=str2double(strValue{2});
                    case 'string'
                        value=strrep(strValue{2},'"','');
                        value=strtrim(value);
                    case 'boolean'
                        value=GlobalParameters.yesno2boolean(strtrim(strValue{2}));
                    end
                else

                end
            end
        end
        function bValue=yesno2boolean(value)
            if strcmpi(value,'yes')
                bValue=true;
            else
                bValue=false;
            end
        end
        function[dateForcStart,dateForcInter]=ForcingTimePar(strForcStart,strForcInter,fmt)
            if strcmpi(fmt,'yyyy\DOY')
                content=strsplit(strForcStart,'\');
                yearStart=str2double(content{1});
                doyStart=str2double(content{2});
                dateForcStart=datenum(yearStart,1,1);
                dateForcStart=dateForcStart+doyStart-1;
            else
                dateForcStart=datenum(strForcStart,fmt);
            end
            dateForcInter=GlobalParameters.CalTimeInterval(strForcInter,fmt);
        end
    end
end