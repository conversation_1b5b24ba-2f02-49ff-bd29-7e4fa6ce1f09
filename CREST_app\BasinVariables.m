classdef BasinVariables<RasterVariables
    properties
        DEM;
        FDR;
        FAC;
        stream;

        GM;
        heightNextToOutlet;

        LenSN;
        LenEW;
        LenCross;
        slope;
        gridArea;
        nextRow;
        nextCol;
        nextLen;
        nextTimeS;
        nextTimeI;








        SRowA;SColA;SFracA;
        SRowB;SColB;SFracB;
        IRowA;IColA;IFracA;
        IRowB;IColB;IFracB;

        RSPassedRow;RSPassedCol;RSStartedRow;RSStartedCol;
        RIPassedRow;RIPassedCol;RIStartedRow;RIStartedCol;

        STCD;
        rowOutlet;
        colOutlet;
        indexOutlet;
        masks;
    end
    properties(Access=private)
        rTimeUnit;
    end
    methods
        function obj=BasinVariables(basicDir,basicFmt,timeMark)
            [fileDEM,fileFDR,fileFAC,fileStream,fileMask,fileGM]=BasinVariables.GenerateFileNames(basicDir,basicFmt);
            switch timeMark
            case 'd'
                obj.rTimeUnit=1/24;
            case 'h'
                obj.rTimeUnit=1;
            case 'u'
                obj.rTimeUnit=60;
            end
            disp('reading geographic data...')
            [obj.DEM,obj.geoTrans,obj.spatialRef]=ReadRaster(fileDEM);
            obj.bGCS=IsGeographic(obj.spatialRef,obj.geoTrans);
            obj.FDR=ReadRaster(fileFDR);
            obj.FAC=ReadRaster(fileFAC);
            obj.ReadGM(fileGM);
            disp('identifying channel cells...')
            obj.stream=ReadRaster(fileStream);
            obj.stream(isnan(obj.stream))=0;
            obj.stream=logical(obj.stream);
            disp('generating basin mask...');
            obj.GetBasinMask();
            disp('generating routing map...')
            obj.AssignNextGroup();
        end
        function CalSlope(obj,mode,rowOutlet,colOutlet)
            obj.rowOutlet=rowOutlet;
            obj.colOutlet=colOutlet;
            obj.indexOutlet=sub2ind(size(obj.basinMask),obj.rowOutlet,obj.colOutlet);
            [rows,columns]=size(obj.DEM);
            index=obj.InRectangle(obj.nextRow,obj.nextCol);
            index=logical(index.*obj.basinMask);

            obj.slope=obj.Initialize();
            demNext=obj.Initialize();
            demNext(index)=obj.DEM(sub2ind([rows,columns],obj.nextRow(index),obj.nextCol(index)));
            demNext(~index)=NaN;
            indexValid=obj.DEM>demNext;
            indexInvalid=logical((~indexValid).*obj.basinMask);


            obj.slope(indexValid)=(obj.DEM(indexValid)-demNext(indexValid))./obj.nextLen(indexValid);




            obj.slope(indexInvalid)=obj.GM./obj.nextLen(indexInvalid);
            switch mode
            case 'mean'
                obj.slope(obj.indexOutlet)=obj.GM./obj.nextLen(obj.indexOutlet);
            case 'real'
                obj.slope(obj.indexOutlet)=(obj.DEM(obj.indexOutlet)-obj.heightNextToOutlet)/obj.nextLen(obj.indexOutlet);
            end
            obj.slope(obj.slope<0)=-obj.slope(obj.slope<0);
            obj.slope(abs(obj.slope)<1e-6)=1e-6;
        end
        function GetSubMasks(obj,outRow,outCol,outName,maskDir)
            disp('delineate subbasins using the watershed algorithm...')
            GDT_Int32=5;
            nOutlets=length(outRow);
            [rows,cols]=size(obj.basinMask);
            obj.masks=false(rows,cols,nOutlets);

            for i=1:nOutlets
                maski=extractbasin_s(obj.FDR,outRow(i),outCol(i));
                fileMask=[maskDir,outName{i},'_mask.tif'];
                obj.masks(:,:,i)=logical(maski);
                maski(~obj.basinMask)=NaN;
                WriteRaster(fileMask,maski,obj.geoTrans,obj.proj,GDT_Int32,'GTiff',-9999);
            end
        end
        function[row,col]=GetStreamRowAndCol(obj)
            [rows,columns]=size(obj.DEM);
            [C,R]=meshgrid(1:columns,1:rows);
            row=R(obj.stream);
            col=C(obj.stream);
        end
        function RunoffAndRoutePre(obj,timeStep,coeM,expM,coeR,coeS,RunoffRows,RunofCols,hasRiverInterflow)






            obj.CalNextTime(coeM,expM,coeR,coeS,hasRiverInterflow);
            [obj.SRowA,obj.SColA,obj.SFracA,...
            obj.SRowB,obj.SColB,obj.SFracB,...
            obj.RSPassedRow,obj.RSPassedCol,...
            obj.RSStartedRow,obj.RSStartedCol]=obj.RouteTreat(timeStep,obj.nextTimeS,RunoffRows,RunofCols);
            [obj.IRowA,obj.IColA,obj.IFracA,...
            obj.IRowB,obj.IColB,obj.IFracB,...
            obj.RIPassedRow,obj.RIPassedCol,...
            obj.RIStartedRow,obj.RIStartedCol]=obj.RouteTreat(timeStep,obj.nextTimeI,RunoffRows,RunofCols);
        end
    end
    methods(Access=private)
        function AssignNextGroup(obj)

            if obj.bGCS
                obj.LenSN=abs(obj.geoTrans(6))*110574.0;

            else

                obj.LenSN=abs(obj.geoTrans(6));
            end
            [rows,columns]=size(obj.DEM);
            obj.nextRow=obj.Initialize();
            obj.nextCol=obj.Initialize();
            obj.nextLen=obj.Initialize();
            obj.gridArea=obj.Initialize();
            obj.LenEW=obj.Initialize();
            obj.LenCross=obj.Initialize();
            r=1:rows;
            c=1:columns;
            [C,R]=meshgrid(c,r);
            R=R(obj.basinMask);
            C=C(obj.basinMask);

            if obj.bGCS
                [lat,~]=RowCol2Proj(obj.geoTrans,R,C);

                obj.LenEW(obj.basinMask)=obj.LenSN*cosd(lat);
            else
                obj.LenEW(obj.basinMask)=abs(obj.geoTrans(2));
            end
            obj.LenCross(obj.basinMask)=sqrt(obj.LenEW(obj.basinMask).^2+obj.LenSN^2);
            obj.gridArea(obj.basinMask)=obj.LenSN*obj.LenEW(obj.basinMask)*1e-6;

            dirIndices=2.^(0:7);
            dirR={'R','1+R','1+R','1+R','R','-1+R','-1+R','-1+R'};
            dirC={'1+C','1+C','C','-1+C','-1+C','-1+C',...
            'C','1+C'};
            dirL={'LenEW','LenCross','LenSN','LenCross',...
            'LenEW','LenCross','LenSN','LenCross'};
            fdr=obj.FDR(obj.basinMask);
            nData=length(fdr);
            nr=zeros(nData,1);
            nc=zeros(nData,1);
            nl=zeros(nData,1);
            for i=1:8
                indices=fdr==dirIndices(i);
                cmdR=strcat('nr(indices)=',dirR{i},'(indices);');
                cmdC=strcat('nc(indices)=',dirC{i},'(indices);');
                if isempty(strfind(dirL{i},'LenSN'))
                    cmdL=strcat('len=obj.',dirL{i},'(obj.basinMask);',...
                    'nl(indices)=len(indices);');
                else
                    cmdL=strcat('nl(indices)=obj.',dirL{i},';');
                end
                eval(cmdR);
                eval(cmdC);
                eval(cmdL);
            end

            obj.nextRow(obj.basinMask)=nr;
            obj.nextCol(obj.basinMask)=nc;
            obj.nextLen(obj.basinMask)=nl;

        end

        function CalSlopeXXW(obj)
            [rows,columns]=size(obj.DEM);
            index=obj.InRectangle(obj.nextRow,obj.nextCol);
            index=logical(index.*obj.basinMask);
            obj.slope=obj.Initialize();

            demNext=obj.Initialize();
            demNext(index)=obj.DEM(sub2ind([rows,columns],obj.nextRow(index),obj.nextCol(index)));
            demNext(isnan(demNext))=-9999;
            indexValid=logical((obj.DEM>demNext).*obj.basinMask);
            indexInvalid=logical((~indexValid).*obj.basinMask);
            obj.slope(indexValid)=(obj.DEM(indexValid)-demNext(indexValid))./obj.nextLen(indexValid);

            obj.slope(indexInvalid)=obj.CalSlopeNoData(indexInvalid);
            obj.slope(obj.slope<0)=-obj.slope(obj.slope<0);
            obj.slope(abs(obj.slope)<1e-6)=1e-6;
        end
        function CalSlopeTiger(obj)

            [rows,columns]=size(obj.DEM);
            index=obj.InRectangle(obj.nextRow,obj.nextCol);
            index=logical(index.*obj.basinMask);
            obj.slope=obj.Initialize();

            demNext=obj.Initialize();
            demNext(index)=obj.DEM(sub2ind([rows,columns],obj.nextRow(index),obj.nextCol(index)));
            demNext(isnan(demNext))=-9999;
            indexValid=logical((obj.DEM>demNext).*obj.basinMask);
            indexInvalid=logical((~indexValid).*obj.basinMask);
            obj.slope(indexValid)=(obj.DEM(indexValid)-demNext(indexValid))./obj.nextLen(indexValid);
            obj.slope(indexInvalid)=obj.GM./obj.nextLen(indexInvalid);
        end
        function CalNextTime(obj,coeM,expM,coeR,coeS,hasRiverInterflow)
            speedVegLocalNext=0.5;

            speed=obj.Initialize();
            obj.nextTimeS=obj.Initialize();
            speed(obj.basinMask)=coeM(obj.basinMask)*speedVegLocalNext.*obj.slope(obj.basinMask).^expM(obj.basinMask);
            speed(obj.stream)=speed(obj.stream).*coeR(obj.stream);
            obj.nextTimeS(obj.basinMask)=obj.nextLen(obj.basinMask)./speed(obj.basinMask)/3600*obj.rTimeUnit;
            obj.nextTimeI=obj.Initialize();
            obj.nextTimeI(obj.basinMask)=obj.nextTimeS(obj.basinMask)./coeS(obj.basinMask);
            if~hasRiverInterflow
                obj.nextTimeI(obj.stream)=obj.nextTimeS(obj.stream);
            end
        end
        function[toRowA,toColA,toPerA,toRowB,toColB,toPerB,...
            RPassedRow,RPassedCol,RStartedRow,RStartedCol]=RouteTreat(obj,timeStep,nextTimeX,RSitesRow,RSitesCol)






            toRowA=obj.Initialize();
            toColA=obj.Initialize();
            toPerA=obj.Initialize();
            toRowB=obj.Initialize();
            toColB=obj.Initialize();
            toPerB=obj.Initialize();
            RPassedRow=[];
            RPassedCol=[];
            RStartedRow=[];
            RStartedCol=[];
            siteInd=sub2ind(size(obj.basinMask),RSitesRow,RSitesCol);

            [rows,columns]=size(obj.DEM);
            [C,R]=meshgrid(1:columns,1:rows);

            toRowB(obj.basinMask)=R(obj.basinMask);
            toColB(obj.basinMask)=C(obj.basinMask);
            toPerB(obj.basinMask)=0;
            index=toPerB<timeStep;
            bSetOff=false;
            while any(index(:))

                rowB=toRowB(index);
                colB=toColB(index);
                perB=toPerB(index);
                rowA=rowB;
                colA=colB;


                toPerA(index)=perB;
                toRowA(index)=rowA;
                toColA(index)=colA;



                [indA,indexIn]=obj.sub2indInBasin(rowA,colA);
                if bSetOff
                    RStart=R(index);
                    CStart=C(index);
                    Lia=ismember(indA,siteInd);
                    RStartedRow=[RStartedRow;RStart(Lia)];
                    RStartedCol=[RStartedCol;CStart(Lia)];
                    RPassedRow=[RPassedRow;rowA(Lia)];
                    RPassedCol=[RPassedCol;colA(Lia)];
                end




                rowB(~indexIn)=NaN;
                colB(~indexIn)=NaN;
                perB(~indexIn)=Inf;

                rowB(indexIn)=obj.nextRow(indA(indexIn));
                colB(indexIn)=obj.nextCol(indA(indexIn));
                perB(indexIn)=perB(indexIn)+nextTimeX(indA(indexIn));

                toRowB(index)=rowB;
                toColB(index)=colB;
                toPerB(index)=perB;

                index(index)=toPerB(index)<timeStep;
                bSetOff=true;
            end
            toPerB(obj.basinMask)=(timeStep-toPerA(obj.basinMask))./...
            (toPerB(obj.basinMask)-toPerA(obj.basinMask));
            toPerA(obj.basinMask)=1-toPerB(obj.basinMask);
        end
        function z_adj=FillAdjDEM(obj,r_adj,c_adj,r,c)



            index=obj.InRectangle(r_adj,c_adj);
            z_adj=zeros(size(index));
            z_adj(index)=obj.DEM(sub2ind(size(obj.DEM),r_adj(index),c_adj(index)));
            z_adj(~index)=obj.DEM(sub2ind(size(obj.DEM),r(~index),c(~index)));


            indexNoData=isnan(z_adj);
            z_adj(indexNoData)=obj.DEM(sub2ind(size(obj.DEM),r(indexNoData),c(indexNoData)));
        end
        function slope=CalSlopeNoData(obj,indexNodata)





            [rows,columns]=size(obj.DEM);
            [C,R]=meshgrid(1:columns,1:rows);
            R=R(indexNodata);
            C=C(indexNodata);
            r_a=R-1;c_a=C-1;r_b=R-1;c_b=C;r_c=R-1;c_c=C+1;
            r_d=R;c_d=C-1;r_f=R;c_f=C+1;
            r_g=R+1;c_g=C-1;r_h=R+1;c_h=C;r_i=R+1;c_i=C+1;

            z_a=obj.FillAdjDEM(r_a,c_a,R,C);
            z_b=obj.FillAdjDEM(r_b,c_b,R,C);
            z_c=obj.FillAdjDEM(r_c,c_c,R,C);
            z_d=obj.FillAdjDEM(r_d,c_d,R,C);
            z_f=obj.FillAdjDEM(r_f,c_f,R,C);
            z_g=obj.FillAdjDEM(r_g,c_g,R,C);
            z_h=obj.FillAdjDEM(r_h,c_h,R,C);
            z_i=obj.FillAdjDEM(r_i,c_i,R,C);


            dzdx=(z_c+2.0*z_f+z_i)-(z_a+2.0*z_d+z_g);
            dzdx=dzdx./(8.0*obj.LenEW(indexNodata));

            dzdy=(z_g+2.0*z_h+z_i)-(z_a+2.0*z_b+z_c);
            dzdy=dzdy/(8.0*obj.LenSN);

            slope=sqrt(dzdx.^2+dzdy.^2);
        end
        function ReadGM(obj,fileName)
            fid=fopen(fileName);
            tline=fgetl(fid);
            obj.GM=str2double(tline);
            tline=fgetl(fid);
            obj.heightNextToOutlet=str2double(tline);
            fclose(fid);
        end
        function GetBasinMask(obj)


            [rows,cols]=size(obj.DEM);
            obj.basinMask=zeros(rows,cols);
            obj.basinMask(isnan(obj.DEM))=0;
            obj.basinMask(~isnan(obj.DEM))=1;
            obj.basinMask=logical(obj.basinMask);

        end
    end

    methods(Static)
        function[fileDEM,fileFDR,fileFAC,fileStream,fileMask,fileGM]=GenerateFileNames(basicDir,basicFmt)
            fileDEM=strcat(basicDir,'dem',basicFmt);
            fileFDR=strcat(basicDir,'fdr',basicFmt);
            fileFAC=strcat(basicDir,'fac',basicFmt);
            fileStream=strcat(basicDir,'stream',basicFmt);
            fileMask=strcat(basicDir,'mask',basicFmt);
            fileGM=strcat(basicDir,'slope.def');
        end

    end
end