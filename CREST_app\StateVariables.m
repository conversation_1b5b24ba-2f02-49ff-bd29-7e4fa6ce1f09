classdef StateVariables<RasterVariables
    properties
        stream;
        W0;
        pW0;

        SS0;
        SI0;
        bDistW0,bDistSS0,bDistSI0;

        excS;
        excI;
        EAct;
        RS;
        RI;
        runoff;
        rain;  % Fixed: Added missing semicolon
        PET;
        rainAct;


        px_rain;
        px_PET;
        px_EAct;
        px_W;
        px_SM;
        px_runoff;
        px_excS;px_excI;
        px_RS;px_RI;
        px_rainAct;



        timeStepInM;




        timeMark;
        resPath;

        hydroSites;


        fileInit;
        bDistributedSoil;
    end
    methods
        function obj=StateVariables(hSites,dirRes,...
            timeMark,timeStepInM,...
            basinMask,streamMask,...
            fileInitCondDir,geoTrans,spatialRef)





            obj.hydroSites=hSites;
            [fileStateTXT,fileStateMat]=StateVariables.GenerateFileNames(fileInitCondDir);
            if exist(fileStateMat,'file')==2
                obj.fileInit=fileStateMat;
                obj.bDistributedSoil=true;
            elseif exist(fileStateTXT,'file')==2
                obj.bDistributedSoil=false;
                obj.fileInit=fileStateTXT;
            end
            obj.geoTrans=geoTrans;
            obj.spatialRef=spatialRef;




            obj.timeMark=timeMark;
            obj.timeStepInM=timeStepInM;
            obj.resPath=dirRes;
            obj.basinMask=basinMask;
            obj.stream=streamMask;










            obj.px_rain=zeros(obj.hydroSites.nTimeSteps,obj.hydroSites.nSites);
            obj.px_rainAct=zeros(obj.hydroSites.nTimeSteps,obj.hydroSites.nSites);
            obj.px_PET=zeros(obj.hydroSites.nTimeSteps,obj.hydroSites.nSites);
            obj.px_EAct=zeros(obj.hydroSites.nTimeSteps,obj.hydroSites.nSites);
            obj.px_W=zeros(obj.hydroSites.nTimeSteps,obj.hydroSites.nSites);
            obj.px_SM=zeros(obj.hydroSites.nTimeSteps,obj.hydroSites.nSites);
            obj.px_runoff=zeros(obj.hydroSites.nTimeSteps,obj.hydroSites.nSites);


        end
        function preset(obj)

            keywordType={'WU0Type','SS0Type','SI0Type'};
            keywordVar={'WU0','SS0','SI0'};
            commentSymbol='#';

            if~obj.bDistributedSoil
                sfid=fopen(obj.fileInit);
                [value,bDistr]=StateVariables.readVarInfo(sfid,keywordType{1},keywordVar{1},commentSymbol);
                obj.bDistW0=bDistr;
                obj.pW0=obj.Initialize();
                obj.pW0(obj.basinMask)=value;
                obj.W0=obj.Initialize();

                [value,bDistr]=StateVariables.readVarInfo(sfid,keywordType{2},keywordVar{2},commentSymbol);
                obj.bDistSS0=bDistr;
                obj.SS0=obj.Initialize();
                obj.SS0(obj.basinMask)=value;

                [value,bDistr]=StateVariables.readVarInfo(sfid,keywordType{3},keywordVar{3},commentSymbol);
                obj.bDistSI0=bDistr;
                obj.SI0=obj.Initialize();
                obj.SI0(obj.basinMask)=value;

                fclose(sfid);
            else
                obj.LoadModelStates(obj.fileInit)
            end
        end
        function reset(obj,WM)

            obj.W0(obj.basinMask)=obj.pW0(obj.basinMask).*WM(obj.basinMask)/100;

            obj.rain=obj.Initialize();
            obj.excS=obj.Initialize();
            obj.excI=obj.Initialize();
            obj.PET=obj.Initialize();
            obj.EAct=obj.Initialize();

            obj.RS=obj.Initialize();
            obj.RI=obj.Initialize();
            obj.runoff=obj.Initialize();
            obj.rainAct=obj.Initialize();

        end
        function CalculateOutletData(obj,timeOfStep,WM,bCalib,masks)
            px_ind=sub2ind(size(obj.basinMask),obj.hydroSites.row,obj.hydroSites.col);
            for i=1:obj.hydroSites.nSites
                if~bCalib
                    obj.px_rain(timeOfStep,i)=mean(obj.rain(masks(:,:,i)))/obj.timeStepInM;
                    obj.px_rainAct(timeOfStep,i)=mean(obj.rainAct(masks(:,:,i)))/obj.timeStepInM;
                    obj.px_PET(timeOfStep,i)=mean(obj.PET(masks(:,:,i)))/obj.timeStepInM;
                    obj.px_EAct(timeOfStep,i)=mean(obj.EAct(masks(:,:,i)))/obj.timeStepInM;
                    obj.px_W(timeOfStep,i)=mean(obj.W0(masks(:,:,i)));
                    obj.px_SM(timeOfStep,i)=mean(obj.W0(masks(:,:,i))./WM(masks(:,:,i)));
                    obj.px_excS(timeOfStep,i)=mean(obj.excS(masks(:,:,i)))/obj.timeStepInM;
                    obj.px_excI(timeOfStep,i)=mean(obj.excI(masks(:,:,i)))/obj.timeStepInM;
                    obj.px_RS(timeOfStep,i)=mean(obj.RS(masks(:,:,i)))/obj.timeStepInM;
                    obj.px_RI(timeOfStep,i)=mean(obj.RI(masks(:,:,i)))/obj.timeStepInM;
                end
            end
            obj.px_runoff(timeOfStep,:)=obj.runoff(px_ind);
        end
        function OutputVar(obj,curDate,bRain,bEPot,bEAct,bSM,bW,bRunoff,bExcS,bExcI,bRS,bRI)
            [fileRain,fileEPot,fileEAct,fileSM,fileW,fileRunoff,fileExcS,fileExcI,fileRS,fileRI]=...
            StateVariables.GenerateStateFileNames(obj.resPath,curDate,obj.timeMark);
            if bRain
                obj.SaveRaster(obj.rain,fileRain);
            end
            if bEPot
                obj.SaveRaster(obj.PET,fileEPot);
            end
            if bEAct
                obj.SaveRaster(obj.EAct,fileEAct);
            end
            if bRunoff
                runoffMat=zeros(size(obj.runoff));
                runoffMat(obj.stream)=obj.runoff(obj.stream);
                obj.SaveRaster(runoffMat,fileRunoff);
            end
            if bExcS
                obj.SaveRaster(obj.excS,fileExcS);
            end
            if bExcI
                obj.SaveRaster(obj.excI,fileExcI);
            end
            if bSM
                obj.SaveRaster(obj.pW0,fileSM);
            end
            if bW
                obj.SaveRaster(obj.W0,fileW);
            end
            if bRS
                obj.SaveRaster(obj.RS,fileRS);
            end
            if bRI
                obj.SaveRaster(obj.RI,fileRI);
            end
        end
        function SaveStates(obj)

            disp('Saving Hydrographs...')
            dates=[];
            for ip=1:obj.hydroSites.nPeriods
                if obj.hydroSites.warmupDate(ip)>obj.hydroSites.startDate(ip)
                    dates=[dates,obj.hydroSites.startDate(ip):obj.hydroSites.timeStep:obj.hydroSites.endDate(ip)];
                else
                    dates=[dates,obj.hydroSites.warmupDate(ip)+obj.hydroSites.timeStep:obj.hydroSites.timeStep:obj.hydroSites.endDate(ip)];
                end
            end
            dates=dates';
            switch obj.timeMark
            case 'd'
                fmt='yyyy/mm/dd';
            case 'h'
                fmt='yyyy/mm/dd:HH';
            case 'u'
                fmt='yyyy/mm/dd:HH:MM';
            end
            for i=1:obj.hydroSites.nSites
                title={'Date','rainAct','rain','PET','Eact','W','SM','excS','excI','RS','RI','R','R_Obs'};
                if ischar(obj.hydroSites.STCD{i})
                    staName=obj.hydroSites.STCD{i};
                else
                    staName=num2str(obj.hydroSites.STCD{i});
                end
                fileName=strcat(obj.resPath,staName,'.csv');
                content=table(cellstr(datestr(dates,fmt)),...
                obj.px_rainAct(:,i),obj.px_rain(:,i),obj.px_PET(:,i),...
                obj.px_EAct(:,i),obj.px_W(:,i),obj.px_SM(:,i),...
                obj.px_excS(:,i),obj.px_excI(:,i),obj.px_RS(:,i),...
                obj.px_RI(:,i),...
                obj.px_runoff(:,i),obj.hydroSites.runoff(:,i),...
                'VariableNames',...
                {'Date','rainAct','rain','PET','Eact','W','SM','excS','excI','RS','RI','R','R_Obs'});
                writetable(content,fileName,'Delimiter',',');
            end
        end
        function SaveModelStates(obj,strDateCur,WM)

            iSS0=obj.SS0;
            iSI0=obj.SI0;
            ipW0=obj.W0./WM*100;
            save(strDateCur,'iSS0','iSI0','ipW0');
            clear iSS0 iSI0 iW0
        end
        function LoadModelStates(obj,strDateLoad)
            S=load(strDateLoad);
            obj.SS0=S.iSS0;
            obj.SI0=S.iSI0;
            obj.pW0=S.ipW0;
            clear S
        end
        function[NSCE,Bias,CC]=GetSingleObjNSCE(obj)
            if length(obj.hydroSites.indexOutlets)>1
                error('multi-site calibration is not supported in the current version');
            elseif isempty(obj.hydroSites.indexOutlets)
                warning('no outlet specified')
                NSCE=NaN;
                Bias=NaN;
                CC=NaN;
                return;
            end
            global diff_g mRunoff_g
            outletRunoff=obj.hydroSites.runoff(:,obj.hydroSites.indexOutlets);
            px_outletRunoff=obj.px_runoff(:,obj.hydroSites.indexOutlets);
            indValid=logical((outletRunoff>0).*(px_outletRunoff>0).*(outletRunoff~=obj.hydroSites.noObserv));
            sumObs=sum(outletRunoff(indValid));
            mRunoff=sumObs/sum(indValid);
            diff=px_outletRunoff(indValid)-outletRunoff(indValid);
            diff_g=diff;
            mRunoff_g=mRunoff;
            NSCE=sum(diff.^2)./...
            sum((outletRunoff(indValid)-mRunoff).^2);
            NSCE=1-NSCE;
            if nargout>1
                Bias=(sum(px_outletRunoff(indValid))/sumObs-1)*100;
                CC=corrcoef(px_outletRunoff(indValid),outletRunoff(indValid));
                CC=CC(1,2);
            end
        end
    end
    methods(Static)
        function[fileStateTXT,fileStateMat]=GenerateFileNames(dirState)
            fileStateTXT=strcat(dirState,'InitialConditions.txt');
            fileStateMat=strcat(dirState,'InitialConditions.mat');
        end
        % Fixed: Added public wrapper for protected readVarInfo method
        function[value,bDistributed]=readVarInfo(gfileID,keywordType,keywordVar,commentSymbol)
            % Call the protected method from parent class RasterVariables
            [value,bDistributed]=RasterVariables.readVarInfo(gfileID,keywordType,keywordVar,commentSymbol);
        end
        function[fileRain,fileEPot,fileEAct,fileSM,fileW,fileRunoff,fileExcS,fileExcI,fileRS,fileRI]=GenerateStateFileNames(dir,date,timeMark)
            switch(timeMark)
            case 'd'
                fmt='yyyymmdd';
            case 'h'
                fmt='yyyymmddHH';
            case 'u'
                fmt='yyyymmddHHMM';
            end
            dateStr=datestr(date,fmt);
            fileRain=[dir,'RainAct_',dateStr,'.tif'];
            fileEPot=[dir,'EPot',dateStr,'.tif'];
            fileEAct=[dir,'EAct',dateStr,'.tif'];
            fileSM=[dir,'SM_',dateStr,'.tif'];
            fileW=[dir,'W_',dateStr,'.tif'];
            fileRunoff=[dir,'runoff_',dateStr,'.tif'];
            fileExcS=[dir,'ExcS_',dateStr,'.tif'];
            fileExcI=[dir,'ExcI_',dateStr,'.tif'];
            fileRS=[dir,'RS_',dateStr,'.tif'];
            fileRI=[dir,'RI_',dateStr,'.tif'];
        end
    end
end