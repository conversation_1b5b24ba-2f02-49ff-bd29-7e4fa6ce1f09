###############################################################################
# CREST Project File
###############################################################################
Version						=			2.1.3
###############################################################################
# MODEL Run Time Information
###############################################################################
TimeMark					=			d	#y(year);m(month);d(day);h(hour);u(minute);s(second)
TimeFormat				=			yyyymmddHH
TimeStep					=			1
StartDate					= 		**********
NLoad					=			0 #Nload=0 regular simulation; NLoad>0, states variables are loaded at warmup dates
WarmupDate				=			**********
EndDate						= 		**********
###############################################################################
# MODEL Switchers
###############################################################################
RunStyle					=			simu # simu, cali_SCEUA, RealTime
Feedback					=			No 	# routing feeds the LSM back
hasRiverInterflow				=			No	#No: all interflow turns to surface flow in the river
UseLAI				=			No		# compute the rainfall interception
###############################################################################
# MODEL Directories
###############################################################################
BasicFormat				=			.tif #any gdal supported formats
BasicPath					=			"G:\simulation\US_Basins\Tar\BASIC\"
###############################################################################
ParamFormat				=			.asc
ParamPath					=			"G:\simulation\US_Basins\Tar\Param\Parameters_nofeed_noRiverInterflow_02-05_simu.txt"
###############################################################################
StateFormat				=			.asc
StatePath					=			"G:\simulation\US_Basins\Tar\States\"
###############################################################################
ICSFormat					=			.asc
ICSPath						=			"G:\simulation\US_Basins\Tar\ICS\"
###############################################################################
# external rainfall settings 
RainFormat				=			.24h.Z
RainDateFormat				=			yyyymmddHH
RainDateConv				=				End
RainStart				=				2002010112
RainDateInterval				=				0000000100
RainPathExt					=			"G:\mete data\StageIV_daily\ST4."
RainTsScaling				=			1
# internal rainfall setting
RainPathInt				=				"G:\simulation\US_Basins\Tar\rains_daily\rain."
###############################################################################
PETFormat					=			.bil
PETDateFormat				=			yymmdd
PETDateConv				=				Begin
PETStart			=			020101
PETDateInterval			=			000001
PETPathExt						=			"G:\mete data\PET_FEWS\et"
PETTsScaling			=			100 # FEWS data has a 100 scaling factor: http://earlywarning.cr.usgs.gov/fews/global/web/readme.php?symbol=pt
# internal PET setting
PETPathInt			=			"G:\simulation\US_Basins\Tar\PETs_daily\PET."
###############################################################################
LAIFormat					=			_mosaic.tif
LAIDateFormat				=			yyyy\DOY
LAIStart			=			2006\001
LAIDateInterval			=			0000\008
LAIPathExt						=			"C:\data\LAI\Mosaic\"
LAITsScaling			=			10 # LAI data has a 10 scaling factor and 255 is water body: http://glass-product.bnu.edu.cn/en
LAIDateConv				=				Center/d/file/wendangziliao/suanfawendang/2014-03-13/GLASS%20LAI%E4%BA%A7%E5%93%81%E7%94%A8%E6%88%B7%E4%BD%BF%E7%94%A8%E6%89%8B%E5%86%8C_v1.1.pdf
LAIPathInt						=			"G:\data\LAI\Mosaic\"
LAIPathInt						=			"G:\simulation\US_Basins\Tar\LAI_daily\"
###############################################################################
ResultFormat			=			asc
ResultPath				=			"G:\simulation\US_Basins\Tar\result\"
###############################################################################
CalibFormat				=			asc
CalibPath					=			"G:\simulation\US_Basins\Tar\Calib\"
CalibMode					=			Parallel # Sequential
###############################################################################
OBSDateFormat					=			yyyymmdd
OBSPath						=			"G:\simulation\US_Basins\Tar\OBS\"
OBSNoDataValue			=				-9999
###############################################################################
# The below data are optional. when RunStyle=cali_SCEUA, they can be omited
###############################################################################
#Outlet Information
###############################################################################
HasOutlet					=			yes
OutletName				=			02083500	#02083500 the filename of observation
SitesShpFile				=		Tar_All_sites.shp  # the shapefile name
###############################################################################
#Grid Outputs
###############################################################################
GOVar_Rain				=			No
GOVar_PET					=			No
GOVar_EPot				=			No
GOVar_EAct				=			No
GOVar_W						=			No
GOVar_SM					=			No
GOVar_R						=			No
GOVar_ExcS				=			No
GOVar_ExcI				=			No
GOVar_RS					=			No
GOVar_RI					=			No
###############################################################################
NumOfOutputDates	=			0 #23
SaveDateFormat				=			"yyyymmddHHMM"
DateOffset				=			000000000030
OutputDate_1			=			2002100800
OutputDate_2			=			2002120500
OutputDate_3			=			2003012400
OutputDate_4			=			2003031700
OutputDate_5			=			2003040700
OutputDate_6			=			2003050600
OutputDate_7			=			2003060600
OutputDate_8			=			2003080300
OutputDate_9			=			2003091700
OutputDate_10			=			2003120600
OutputDate_11			=			2004081400
OutputDate_12			=			2004083000
OutputDate_13			=			2006061200
OutputDate_14			=			2006082900
OutputDate_15			=			2006110500
OutputDate_16			=			2009022800
OutputDate_17			=			2009031500
OutputDate_18			=			2009102700
OutputDate_19			=			2009120200
OutputDate_20			=			2010011700
OutputDate_21			=			2010091900
OutputDate_22			=			2011082300
OutputDate_23			=			2013060800
###############################################################################
DecompBeforeSrc				=				""C:\sys\WinRaR\WinRAR.exe" -ibck x "
DecompBeforeDst 			=				" "
OS									=					"windows"
