classdef RasterVariables<handle
    methods(Access=public,Sealed=true)
        function var=Initialize(obj)


            [rows,columns]=size(obj.basinMask);
            var=zeros(rows,columns);
            var(~obj.basinMask)=NaN;
        end
        function[ind,bInBasin]=sub2indInBasin(obj,matRow,matCol)


            bInBasin=InRectangle(obj,matRow,matCol);
            ind=zeros(length(matRow),1);
            ind(bInBasin)=sub2ind(size(obj.basinMask),matRow(bInBasin),matCol(bInBasin));


            ind(bInBasin)=ind(bInBasin).*obj.basinMask(ind(bInBasin));
            bInBasin(bInBasin)=ind(bInBasin)>0;
            ind(~bInBasin)=NaN;
        end
        function index=InRectangle(obj,matRow,matCol)
            [rows,columns]=size(obj.DEM);
            index=logical((matRow<=rows).*(matRow>0).*(matCol<=columns).*(matCol>0));
        end

        function SaveRaster(obj,mat,fileGeoTif)

            GDT_Float32=6;
            WriteRaster(fileGeoTif,mat,obj.geoTrans,obj.spatialRef,GDT_Float32,'GTiff',-9999);
        end
    end
    methods(Static=true,Access=protected)
        function[value,bDistributed]=readVarInfo(gfileID,keywordType,keywordVar,commentSymbol)
            value=-1;
            bDistributed=-1;
            if~isempty(keywordType)
                while bDistributed==-1
                    tline=fgetl(gfileID);
                    if strcmp(tline(1),commentSymbol)==1
                        continue;
                    end
                    strArr=regexp(tline,commentSymbol,'split');
                    strContent=strArr{1};
                    strContent=strtrim(strContent);
                    if~isempty(strfind(strContent,keywordType))
                        strValue=regexp(strContent,'=','split');
                        if strcmpi(strtrim(strValue{2}),'distributed')==1
                            bDistributed=true;
                        else
                            bDistributed=false;
                        end
                    end
                end
            else
                bDistributed=true;
            end
            while value==-1
                tline=fgetl(gfileID);
                if strcmp(tline(1),commentSymbol)==1
                    continue;
                end
                strArr=regexp(tline,commentSymbol,'split');
                strContent=strArr{1};
                strContent=strtrim(strContent);
                if~isempty(strfind(strContent,keywordVar))
                    strValue=regexp(strContent,'=','split');
                    if~isnan(str2double(strValue{2}))
                        value=str2double(strValue{2});
                    else
                        value=strtrim(strValue{2});
                    end
                end
            end
        end
        function bRes=IsGCS(refMat)
            if(refMat(2,1)<2)
                bRes=true;
            else
                bRes=false;
            end
        end
    end
    properties(Access=public)
        basinMask;
        spatialRef;
        geoTrans;
bGCS
        proj;
    end
    methods(Abstract,Static)
        fileNames=GenerateFileNames(obj,dirFolder)
    end
end