function varargout=funcWrapper(varargin)
% Fixed: Corrected parameter passing to match expected function signature
% Expected call: funcHandle(simObj, arg, keywords)
if length(varargin) < 3
    error('funcWrapper requires at least 3 arguments: func<PERSON>andle, simObj, keywords, arg1, arg2, ...');
end

funcHandle=varargin{1};
simObj=varargin{2};
keywords=varargin{3};
nGroup=length(varargin)-3;
varargout=cell(nGroup,1);

for i=1:nGroup
    arg=varargin{i+3};
    [NSCE,tElapse]=funcHandle(simObj,arg,keywords);
    varargout{i}=-NSCE;  % Fixed: Use -NSCE for correct optimization direction
end
end