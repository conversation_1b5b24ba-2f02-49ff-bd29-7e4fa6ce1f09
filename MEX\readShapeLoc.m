function [xLoc, yLoc, spatialRef, STCD] = readShapeLoc(shapefile, flag)
% readShapeLoc - Replacement for MEX function to read shapefile locations
% 
% Input:
%   shapefile - Path to shapefile
%   flag - Processing flag (usually 0)
%
% Output:
%   xLoc - X coordinates
%   yLoc - Y coordinates  
%   spatialRef - Spatial reference string
%   STCD - Station codes/IDs

try
    % Try to use MATLAB's built-in shapefile reading functions
    if exist('shaperead', 'file')
        % Use Mapping Toolbox shaperead
        S = shaperead(shapefile);
        
        % Extract coordinates
        nPoints = length(S);
        xLoc = zeros(nPoints, 1);
        yLoc = zeros(nPoints, 1);
        STCD = cell(nPoints, 1);
        
        for i = 1:nPoints
            if isfield(S(i), 'X') && isfield(S(i), 'Y')
                xLoc(i) = S(i).X;
                yLoc(i) = S(i).Y;
            elseif isfield(S(i), 'Geometry') && strcmp(S(i).Geometry, 'Point')
                % Handle point geometry
                if isfield(S(i), 'BoundingBox')
                    xLoc(i) = mean(S(i).BoundingBox(:,1));
                    yLoc(i) = mean(S(i).BoundingBox(:,2));
                end
            end
            
            % Try to extract station ID from various possible field names
            if isfield(S(i), 'STCD')
                STCD{i} = S(i).STCD;
            elseif isfield(S(i), 'ID')
                STCD{i} = S(i).ID;
            elseif isfield(S(i), 'Name')
                STCD{i} = S(i).Name;
            elseif isfield(S(i), 'STATION')
                STCD{i} = S(i).STATION;
            else
                STCD{i} = sprintf('Station_%d', i);
            end
        end
        
        % Try to read projection info
        try
            prjFile = strrep(shapefile, '.shp', '.prj');
            if exist(prjFile, 'file')
                fid = fopen(prjFile, 'r');
                spatialRef = fread(fid, '*char')';
                fclose(fid);
            else
                spatialRef = '';
            end
        catch
            spatialRef = '';
        end
        
    else
        % Fallback: create dummy data based on shapefile name
        warning('readShapeLoc: shaperead not available. Creating dummy station data.');

        % Extract station name from filename
        [~, filename, ~] = fileparts(shapefile);

        % Create single dummy station with reasonable coordinates
        % Use coordinates that are likely to be within the basin
        xLoc = 500000;  % Reasonable UTM-like coordinate
        yLoc = 4000000; % Reasonable UTM-like coordinate

        % Use the filename as STCD (this should match the observation file naming)
        % If filename contains numbers, extract them as the station ID
        if any(isstrprop(filename, 'digit'))
            % Extract numeric part from filename
            numericPart = regexp(filename, '\d+', 'match');
            if ~isempty(numericPart)
                STCD = numericPart;  % Use the first numeric sequence
            else
                STCD = {filename};
            end
        else
            STCD = {filename};
        end
        spatialRef = '';
        
        % Try to read some basic info if it's a text file or has associated files
        try
            % Look for associated files with coordinates
            baseDir = fileparts(shapefile);
            files = dir(fullfile(baseDir, [filename, '*']));
            
            for i = 1:length(files)
                if contains(files(i).name, '.csv') || contains(files(i).name, '.txt')
                    % Try to read coordinate data from CSV/text file
                    data = readtable(fullfile(baseDir, files(i).name));
                    if height(data) > 0
                        % Look for coordinate columns
                        varNames = data.Properties.VariableNames;
                        xCol = find(contains(lower(varNames), {'x', 'lon', 'long'}), 1);
                        yCol = find(contains(lower(varNames), {'y', 'lat'}), 1);
                        
                        if ~isempty(xCol) && ~isempty(yCol)
                            xLoc = data{1, xCol};
                            yLoc = data{1, yCol};
                        end
                    end
                    break;
                end
            end
        catch
            % Keep dummy values
        end
    end
    
    % Ensure outputs are column vectors
    xLoc = xLoc(:);
    yLoc = yLoc(:);
    if ~iscell(STCD)
        STCD = {STCD};
    end
    STCD = STCD(:);
    
catch ME
    % If all else fails, create minimal dummy data
    warning('readShapeLoc: Error reading shapefile %s: %s. Creating dummy data.', shapefile, ME.message);
    
    [~, filename, ~] = fileparts(shapefile);
    xLoc = 0;
    yLoc = 0;
    STCD = {filename};
    spatialRef = '';
end

end
