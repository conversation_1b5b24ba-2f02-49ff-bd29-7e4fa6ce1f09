function mask=extractbasin_s(fdir,row,col)









































    new_row=round(row);new_col=round(col);























































    outlet_row=new_row;
    outlet_col=new_col;










    mask=zeros(size(fdir));
    Lgrid=nan(size(fdir));
    Dgrid=nan(size(fdir));
    drain_group=[];
    new_drain_group=[];
    new_level_length=[];
    level_length=[];


    mask(outlet_row,outlet_col)=1;






    fdircodes=[2,4,8,16,32,64,128,1];




    surroundings=[new_row-1,new_col-1;
    new_row-1,new_col;
    new_row-1,new_col+1;
    new_row,new_col+1;
    new_row+1,new_col+1;
    new_row+1,new_col;
    new_row+1,new_col-1;
    new_row,new_col-1];


    [surr_r]=find(min(surroundings,[],2)>0&surroundings(:,1)<=size(fdir,1)&surroundings(:,2)<=size(fdir,2));
    surroundings=surroundings(surr_r,:);
    fdircodes_surr=fdircodes(surr_r);



    n_accounted_cells=0;
    n_accounted_cells0=-1;
    cont=0;
    dist_level=1;
    for neig=1:size(surroundings,1)
        if(fdir(surroundings(neig,1),surroundings(neig,2))==fdircodes_surr(neig))
            cont=cont+1;
            n_accounted_cells=n_accounted_cells+1;
            drain_group(cont,:)=surroundings(neig,:);

            mask(surroundings(neig,1),surroundings(neig,2))=1;


        end
    end



    while n_accounted_cells>n_accounted_cells0
        n_accounted_cells0=n_accounted_cells;
        cont=0;

        dist_level=dist_level+1;
        for cells=1:size(drain_group,1)
            new_row=drain_group(cells,1);
            new_col=drain_group(cells,2);



            surroundings=[new_row-1,new_col-1;
            new_row-1,new_col;
            new_row-1,new_col+1;
            new_row,new_col+1;
            new_row+1,new_col+1;
            new_row+1,new_col;
            new_row+1,new_col-1;
            new_row,new_col-1];


            [surr_r]=find(min(surroundings,[],2)>0&surroundings(:,1)<=size(fdir,1)&surroundings(:,2)<=size(fdir,2));
            surroundings=surroundings(surr_r,:);
            fdircodes_surr=fdircodes(surr_r);


            for neig=1:size(surroundings,1)
                if(fdir(surroundings(neig,1),surroundings(neig,2))==fdircodes_surr(neig))
                    cont=cont+1;
                    n_accounted_cells=n_accounted_cells+1;
                    new_drain_group(cont,:)=surroundings(neig,:);
                    mask(surroundings(neig,1),surroundings(neig,2))=1;



                end
            end
        end

        drain_group=new_drain_group;

        new_drain_group=[];

    end
end